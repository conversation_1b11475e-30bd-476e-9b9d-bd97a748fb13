<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.FileUploader">
        <t t-if="state.isUploading and props.showUploadingText">Uploading...</t>
        <span t-else="" t-on-click.prevent="onSelectFileButtonClick" style="display:contents">
            <t t-slot="toggler"/>
        </span>
        <t t-slot="default"/>
        <input
            type="file"
            t-att-name="props.inputName"
            t-ref="fileInput"
            t-attf-class="o_input_file d-none {{ props.fileUploadClass or '' }}"
            t-att-multiple="props.multiUpload ? 'multiple' : false" t-att-accept="props.acceptedFileExtensions or '*'"
            t-on-change="onFileChange"
        />
    </t>

</templates>
