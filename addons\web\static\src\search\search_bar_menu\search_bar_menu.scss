// SearchBar Menu
// ============================================================================
$o-search-bar-menu-max-width: calc(100vw - #{map-get($spacers, 3) * 2 });

.o_search_bar_menu {

    max-width: $o-search-bar-menu-max-width;
    .o_dropdown_container {
        border-color: $dropdown-divider-bg !important;
        min-width: 200px;
    }

    .o_accordion_values .o_input {
        max-width: 17ch;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    @include media-breakpoint-up(lg) {
        .o_dropdown_container {
            max-width: calc(#{$o-search-bar-menu-max-width} / 6);
        }
    }
}
