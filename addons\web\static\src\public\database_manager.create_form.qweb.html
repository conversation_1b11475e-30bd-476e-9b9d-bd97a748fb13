<t>
    <t t-call="master_input" />
    <div class="row mb-3">
        <label for="dbname" class="col-md-4 col-form-label">Database Name</label>
        <div class="col-md-8">
            <input id="dbname" type="text" name="name" class="form-control" required="required" autocomplete="off" t-att-pattern="pattern" title="Only alphanumerical characters, underscore, hyphen and dot are allowed"/>
        </div>
    </div>
    <div class="row mb-3">
        <label for="login" class="col-md-4 col-form-label">Email</label>
        <div class="col-md-8">
            <input id="login" type="text" name="login" class="form-control" required="required" autocomplete="off"/>
        </div>
    </div>
    <div class="row mb-3">
        <label for="password" class="col-md-4 col-form-label">Password</label>
        <div class="col-md-8">
            <div class="input-group">
                <input id="password" type="password" name="password" class="form-control" required="required" autocomplete="off"/>
                <span class="fa fa-eye o_little_eye input-group-text" aria-hidden="true" style="cursor: pointer;"></span>
            </div>
        </div>
    </div>
    <div class="row mb-3">
        <label for="phone" class="col-md-4 col-form-label">Phone Number</label>
        <div class="col-md-8">
            <input id="phone" type="tel" name="phone" class="form-control" autocomplete="off"/>
        </div>
    </div>
    <div class="row mb-3">
        <label for="lang" class="col-md-4 col-form-label">Language</label>
        <div class="col-md-8">
            <select id="lang" name="lang" class="form-select" required="required" autocomplete="off">
                <t t-foreach="langs" t-as="lang">
                    <t t-if="lang[0] == 'en_US'">
                        <option t-att-value="lang[0]" t-out="lang[1]" selected="selected"/>
                    </t>
                    <t t-else="">
                        <option t-att-value="lang[0]" t-out="lang[1]"/>
                    </t>
                </t>
            </select>
        </div>
    </div>
    <div class="row mb-3">
        <label for="country" class="col-md-4 col-form-label ">Country</label>
        <div class="col-md-8">
            <select id="country" name="country_code" class="form-select" autocomplete="off">
                <option value=""></option>
                <t t-foreach="countries" t-as="country">
                    <option t-att-value="country[0]" t-out="country[1]"/>
                </t>
            </select>
        </div>
    </div>
    <div class="row mb-3">
        <label for="load_demo_checkbox" class="col-md-4 col-form-label">Demo Data</label>
        <div class="col-md-8">
            <input type="checkbox" id="load_demo_checkbox" class="form-control-sm" name="demo" value="1" />
        </div>
    </div>
</t>
