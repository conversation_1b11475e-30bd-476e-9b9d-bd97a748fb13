<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="web.FloatField">
        <span t-if="props.readonly" t-esc="formattedValue" />
        <input 
            t-else="" 
            t-on-focusin="onFocusIn" 
            t-on-focusout="onFocusOut" 
            t-att-id="props.id" 
            t-ref="numpadDecimal"  
            t-att-placeholder="props.placeholder" 
            t-att-type="props.inputType" 
            inputmode="decimal" 
            class="o_input" 
            autocomplete="off"
            t-att-step="props.step" />
    </t>

</templates>
