@mixin o-kanban-colorpicker {
    max-width: 150px;
    padding: 3px ($o-dropdown-hpadding - $o-kanban-inner-hmargin) 3px $o-dropdown-hpadding;

    > li {
        display: inline-block;
        margin: $o-kanban-inner-hmargin $o-kanban-inner-hmargin 0 0;
        border: 1px solid white;
        box-shadow: 0 0 0 1px map-get($grays, '300');

        > a {
            display: block;

            &::after {
                content: "";
                display: block;
                width: 20px;
                height: 15px;
            }
        }

        // No Color
        &:first-child > a:after {
            background: linear-gradient(45deg, rgba($dropdown-bg, 0) 0%, rgba($dropdown-bg, 0) 48%, $danger 48%, $danger 52%, rgba($dropdown-bg, 0) 52%, rgba($dropdown-bg, 0) 100%);
        }
    }
}

@mixin o-kanban-colorpicker-colors {
    @for $size from 2 through length($o-colors) {
        // Note: the first color is not defined as it is the 'no color' for kanban
        .o_kanban_color_#{$size - 1}:after {
            background-color: nth($o-colors, $size);
        }
    }
}

.o_kanban_colorpicker {
    @include o-kanban-colorpicker-colors();
    @include o-kanban-colorpicker();
}
