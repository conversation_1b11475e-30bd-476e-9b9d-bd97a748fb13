<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.PercentPieField">
        <figure class="o_pie mb-0 rounded-circle"
                t-attf-style="background: conic-gradient(
                    var(--Percent<PERSON><PERSON>Field-color-active) 0% {{props.record.data[props.name]}}%,
                    var(--PercentPieField-color-static) 0% 100%
                )"
        />

        <div class="o_pie_info">
            <span class="o_pie_value" t-esc="formattedValue + '%'"/>
            <span class="o_pie_text" t-esc="props.string"/>
        </div>
    </t>

</templates>
