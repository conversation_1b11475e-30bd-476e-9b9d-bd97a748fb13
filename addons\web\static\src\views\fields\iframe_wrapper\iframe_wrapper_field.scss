$preview_height: 1464;
$preview_width: 1040;
$preview_scale: 0.50;

@mixin o_preview_iframe_styling($scale) {

  .o_preview_iframe_wrapper {
    overflow: hidden;
    width: ($preview_width * $scale) + 0px;
    height: ($preview_height * $scale) + 0px;
    position: relative;
    margin: 8px;
  }

  .o_preview_iframe {
    width: $preview_width + 0px;
    height: $preview_height + 0px;
    border: 2px solid lightgrey;

    padding-top: 30px;
    padding-bottom: 30px;

    -ms-zoom: 0.5;
    -moz-transform: scale($scale);
    -moz-transform-origin: 0 0;
    -o-transform: scale($scale);
    -o-transform-origin: 0 0;
    -webkit-transform: scale($scale);
    -webkit-transform-origin: 0 0;
  }
}


@include o_preview_iframe_styling($preview_scale)
@media (max-width: 1488px), (max-height: 950px) {
  @include o_preview_iframe_styling($preview_scale * 0.95)
}

@media (max-width: 1488px), (max-height: 900px) {
    @include o_preview_iframe_styling($preview_scale * 0.75);
}

@media (max-width: 600px) {
  @include o_preview_iframe_styling($preview_scale * 0.60)
}
