<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.CopyClipboardField">
        <div class="d-grid rounded-2 overflow-hidden">
            <Field t-props="fieldProps"/>
            <CopyButton className="copyButtonClassName" content="props.record.data[props.name]" icon="copyButtonIcon" successText="successText"/>
        </div>
    </t>

    <t t-name="web.CopyClipboardButtonField">
        <CopyButton t-if="props.record.data[props.name]" className="copyButtonClassName" content="props.record.data[props.name]" copyText="copyText" disabled="disabled" successText="successText"/>
    </t>

</templates>
