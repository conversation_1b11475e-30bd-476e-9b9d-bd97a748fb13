<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.SelectionField">
        <t t-if="props.readonly">
            <span t-esc="string" t-att-raw-value="value" />
        </t>
        <t t-else="">
            <select
                class="o_input pe-3"
                t-on-change="onChange"
                t-on-click.stop="() =&gt; {}"
                t-att-id="props.id">
                <option
                    t-att-selected="false === value"
                    t-att-value="stringify(false)"
                    t-esc="this.props.placeholder || ''"
                    t-attf-style="{{ props.required ? 'display:none' : '' }}"
                />
                <t t-foreach="options" t-as="option" t-key="option[0]">
                    <option
                        t-att-selected="option[0] === value"
                        t-att-value="stringify(option[0])"
                        t-esc="option[1]"
                    />
                </t>
            </select>
        </t>
    </t>

</templates>
