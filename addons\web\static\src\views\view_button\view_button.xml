<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

  <t t-name="web.views.ViewButton">
    <t t-tag="props.tag"
        t-att="props.attrs"
        t-att-id="props.id"
        t-att-class="getClassName()"
        t-att-disabled="disabled"
        t-att-name="props.clickParams.name"
        t-att-special="props.clickParams.special"
        t-att-href="props.tag === 'a' and '#'"
        t-att-style="props.style"
        t-att-type="clickParams.type"
        t-att-data-tooltip-template="hasBigTooltip ? 'views.ViewButtonTooltip' : false"
        t-att-data-tooltip-info="hasBigTooltip ? tooltip : false"
        t-att-data-tooltip="hasSmallToolTip ? props.title : false"
        t-att-tabindex="props.tabindex"
        t-on-click.stop="onClick"
    >
      <t t-if="icon" t-tag="icon.tag" t-att-class="icon.class + (props.string ? ' me-1' : '')" t-att-src="icon.src"/>
      <t t-slot="contents"><span t-if="props.string" t-esc="props.string"/></t>
    </t>
  </t>

  <t t-name="views.ViewButtonTooltip">
      <t t-if="debug || button.help">
        <div class="o-tooltip--string" t-if="debug || button.string" role="tooltip">
            <t t-if="debug">
                Button
                <t t-if="button.string">: </t>
                <t t-if="!button.string"> (no string)</t>
            </t>
            <t t-esc="button.string"/>
        </div>
        <p t-if="button.title" class="o-tooltip--help" role="tooltip">
            <t t-esc="button.title"/>
        </p>
        <p t-if="button.help" class="o-tooltip--help" role="tooltip">
            <t t-esc="button.help"/>
        </p>
        <ul t-if="debug" class="o-tooltip--technical" role="tooltip">
            <li data-item="object">
                <span class="o-tooltip--technical--title">Object:</span>
                <t t-esc="model"/>
            </li>
            <li t-if="button.context" data-item="context">
                <span class="o-tooltip--technical--title">Context:</span>
                <t t-esc="button.context || context"/>
            </li>
            <li t-if="'invisible' in button" data-item="invisible">
                <span class="o-tooltip--technical--title">Invisible:</span>
                <t t-esc="button.invisible"/>
            </li>
            <li t-if="'column_invisible' in button" data-item="column_invisible">
                <span class="o-tooltip--technical--title">Column invisible:</span>
                <t t-esc="button.column_invisible"/>
            </li>
            <li t-if="'required' in button" data-item="required">
                <span class="o-tooltip--technical--title">Required:</span>
                <t t-esc="button.required"/>
            </li>
            <li t-if="'readonly' in button" data-item="readonly">
                <span class="o-tooltip--technical--title">Readonly:</span>
                <t t-esc="button.readonly"/>
            </li>
            <li t-if="button.special" data-item="special">
                <span class="o-tooltip--technical--title">Special:</span>
                <t t-esc="button.special"/>
            </li>
            <t t-set="button_type" t-value="button.type"/>
            <li t-if="button_type" data-item="button_type">
                <span class="o-tooltip--technical--title">Button Type:</span>
                <t t-esc="button_type"/>
            </li>
            <li t-if="button_type === 'object'" data-item="button_method">
                <span class="o-tooltip--technical--title">Method:</span>
                <t t-esc="button.name"/>
            </li>
            <li t-if="button_type === 'action'" data-item="button_action">
                <span class="o-tooltip--technical--title">Action ID:</span>
                <t t-esc="button.name"/>
            </li>
        </ul>
      </t>
  </t>
</templates>
