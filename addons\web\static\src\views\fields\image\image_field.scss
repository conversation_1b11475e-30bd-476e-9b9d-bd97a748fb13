.o_field_image {

    > div {
        height: 100%;
        width: 100%;

        > img {
            background-color: var(--ImageField-background-color, transparent);
        }
    }

    button {
        transition: opacity ease 400ms;
        width: 26px;
        height: 26px;
    }

    .o_mobile_controls {
        button {
            width: 30px;
            height: 30px;
            padding: 6px !important;
        }
    }

    &.o_field_invalid img {
        border: 1px solid map-get($theme-colors, 'danger');
    }
}

.o_image_zoom {
    img {
        max-width: 100%;
        max-height: 50vh;
    }
}
