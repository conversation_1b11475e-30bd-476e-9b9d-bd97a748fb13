// ------- View with SearchPanel -------

.o_component_with_search_panel,
.o_controller_with_searchpanel {
    display: flex;
    align-items: flex-start;

    .o_renderer,
    .o_renderer_with_searchpanel {
        flex: 1 1 100%;
        overflow: auto; // make the renderer and search panel scroll individually
        max-height: 100%;
        position: relative;
        height: 100%;

        @include media-breakpoint-down(md) {
            overflow: visible;
        }    
    }
}

// ------- SearchPanel -------

.o_search_panel {
    --treeEntry-padding-h: #{map-get($spacers, 3)};

    width: var(--SearchPanel-width, #{$o-search-panel-width});
    font-size: var(--SearchPanel-fontSize, #{$o-search-panel-font-size});

    @include media-breakpoint-up(md) {
        border-right: $border-width solid $border-color;
    }

    .o_toggle_fold {
        width: map-get($spacers, 3);
    }

    .o_search_panel_counter {
        font-variant-numeric: tabular-nums;
    }
}

.o_search_panel_resize {
    cursor: col-resize;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: 0px -2px;
    width: 10px;
    height: 100%;
    z-index: 1;
}

.o_search_panel_sidebar {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    width: 3rem;
    @include media-breakpoint-up(md) {
        border-right: $border-width solid $border-color;
    }
}

.o_mobile_search_content {
    --SearchPanel-width: 100%;
    --SearchPanel-fontSize: 1.1em;

    --treeEntry-padding-v: #{$list-group-item-padding-y * 2};
}

@include media-breakpoint-down(md) {
    .o_component_with_search_panel,
    .o_controller_with_searchpanel {
        flex-direction: column;
        align-items: initial;

        .o_renderer_with_searchpanel.o_list_view {
            width: 100%;
        }
    }
}
