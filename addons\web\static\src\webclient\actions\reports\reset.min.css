/*

HTML5 CSS Reset
Based on <PERSON>'s CSS Reset
and html5doctor.com HTML5 Reset

Copyright (c) 2011 736 Computing Services Limited
Released under the MIT license. http://opensource.736cs.com/licenses/mit

*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
audio,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
mark,
menu,
meter,
nav,
output,
progress,
section,
summary,
time,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

body {
  line-height: 1;
}

article,
aside,
dialog,
figure,
footer,
header,
hgroup,
nav,
section,
blockquote {
  display: block;
}

nav ul {
  list-style: none;
}

ol {
  list-style: decimal;
}

ul {
  list-style: disc;
  padding-left: 40px;
}

ul ul {
  list-style: circle;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

ins {
  text-decoration: underline;
}

del {
  text-decoration: line-through;
}

mark {
  background: none;
}

abbr[title],
dfn[title] {
  border-bottom: 1px dotted #000;
  cursor: help;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

input[type="submit"],
input[type="button"],
button {
  padding: 0 !important;
  margin: 0 !important;
}

input,
select,
a img {
  vertical-align: middle;
}
