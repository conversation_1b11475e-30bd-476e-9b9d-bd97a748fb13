<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.FormCogMenu" t-inherit="web.CogMenu">
        <xpath expr="//div[hasclass('o_cp_action_menus')]" position="attributes">
            <attribute name="t-if">env.isSmall or hasItems</attribute>
        </xpath>
        <xpath expr="//t[@t-if='state.printItems.length']" position="before">
            <t t-slot="default"/>
        </xpath>
        <xpath expr="//t[@t-if='state.printItems.length']/Dropdown" position="before">
            <div role="separator" class="dropdown-divider"/>
        </xpath>
    </t>

</templates>
