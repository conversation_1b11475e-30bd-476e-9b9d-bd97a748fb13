<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.KanbanRecord">
        <article
            t-att-class="getRecordClasses()"
            t-att-data-id="props.record.id"
            t-att-tabindex="props.record.model.useSampleModel ? -1 : 0"
            t-on-click="onGlobalClick"
            t-ref="root">
                <t t-call="{{ this.mainTemplate }}" t-call-context="this.renderingContext"/>
                <t t-call="{{ this.constructor.menuTemplate }}"/>
        </article>
    </t>

    <t t-name="web.KanbanRecordMenu">
        <div t-if="showMenu" class="o_dropdown_kanban bg-transparent position-absolute end-0 top-0 w-auto">
            <Dropdown menuClass="getMenuClasses()" position="'bottom-end'">
                <button class="btn o-no-caret rounded-0" title="Dropdown menu">
                    <span class="fa fa-ellipsis-v"/>
                </button>
                <t t-set-slot="content">
                    <KanbanDropdownMenuWrapper>
                        <t t-call="{{ templates[this.menuTemplateName] }}" t-call-context="renderingContext"/>
                    </KanbanDropdownMenuWrapper>
                </t>
            </Dropdown>
        </div>
    </t>
</templates>
