<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.TranslationDialog">
        <Dialog title="title">
            <div class="o_translation_dialog">
                <t t-foreach="terms" t-as="term" t-key="term.id">
                    <div class="row">
                        <div
                            class="col-12"
                            t-att-class="{ o_language_current: term.lang === user.lang }"
                            t-attf-class="col-lg-{{ props.showSource ? 2 : 3 }}"
                        >
                            <t t-esc="term.langName" />
                        </div>
                        <t t-if="props.showSource">
                            <div class="col-12 col-lg-3 source">
                                <t t-esc="term.source" />
                            </div>
                        </t>
                        <div class="col-12 translation" t-attf-class="col-lg-{{ props.showSource ? 7 : 9 }}">
                            <t t-if="props.isText">
                                <textarea
                                    class="o_field_text o_field_translate o_field_widget o_input"
                                    t-att-value="term.value"
                                    t-att-data-id="term.id"
                                    t-att-rows="props.showSource ? 2 : 5"
                                    t-on-change="(ev) => this.updatedTerms[term.id] = ev.target.value"
                                />
                            </t>
                            <t t-else="">
                                <input
                                    type="text"
                                    class="o_field_char o_input"
                                    t-att-value="term.value"
                                    t-att-data-id="term.id"
                                    t-on-change="(ev) => this.updatedTerms[term.id] = ev.target.value"
                                />
                            </t>
                        </div>
                    </div>
                </t>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="onSave">Save</button>
                <button class="btn" t-on-click="props.close">Discard</button>
            </t>
        </Dialog>
    </t>

</templates>
