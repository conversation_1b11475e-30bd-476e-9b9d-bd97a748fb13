<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.ViewScaleSelector">
        <div t-if="Object.keys(props.scales).length > 1" t-att-class="`btn-group o_view_scale_selector ${props.dropdownClass || ''}`">
            <Dropdown>
                <button class="btn btn-secondary scale_button_selection o-dropdown-caret" data-hotkey="v">
                    <t t-esc="props.scales[props.currentScale].description" />
                </button>
                <t t-set-slot="content">
                    <t t-foreach="scales" t-as="scale" t-key="scale.key">
                        <DropdownItem
                            class="`o_scale_button_${scale.key} ${scale.key === props.currentScale ? 'active' : ''}`"
                            onSelected="() => this.props.setScale(scale.key)"
                            attrs="{ 'data-hotkey': scale.hotkey or scale.key[0] }"
                        >
                            <t t-esc="scale.description" />
                        </DropdownItem>
                    </t>
                    <div t-if="this.props.isWeekendVisible !== undefined" class="dropdown-divider" role="separator"/>
                    <DropdownItem
                            t-if="this.props.isWeekendVisible !== undefined"
                            class="`o_show_weekends ${props.isWeekendVisible ? 'active' : ''} ${props.currentScale === 'day' ? 'disabled' : ''}`"
                            onSelected="props.toggleWeekendVisibility"
                        >
                        Show weekends
                    </DropdownItem>
                </t>
            </Dropdown>
        </div>
    </t>

</templates>
