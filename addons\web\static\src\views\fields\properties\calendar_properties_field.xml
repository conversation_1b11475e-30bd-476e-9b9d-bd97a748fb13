<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <div t-name="web.CalendarPropertiesField" t-ref="properties" class="d-flex flex-column gap-3">
        <div class="o_calendar_property_field d-flex"
            t-foreach="propertiesList"
            t-as="propertyConfiguration"
            t-key="propertyConfiguration.name"
            t-if="propertyConfiguration.value and propertyConfiguration.view_in_cards">
            <span class="fw-bold me-2" t-out="propertyConfiguration.string"/>
            <div class="text-truncate">
                <PropertyValue
                    id="generateUniqueDomID()"
                    canChangeDefinition="state.canChangeDefinition"
                    comodel="propertyConfiguration.comodel || ''"
                    context="props.context"
                    domain="propertyConfiguration.domain || '[]'"
                    readonly="props.readonly"
                    selection="propertyConfiguration.selection"
                    string="propertyConfiguration.string"
                    tags="propertyConfiguration.tags"
                    type="propertyConfiguration.type"
                    value="propertyConfiguration.value"
                    onChange.bind="() => {}"
                    onTagsChange.bind="() => {}"
                />
            </div>
        </div>
    </div>
</templates>
