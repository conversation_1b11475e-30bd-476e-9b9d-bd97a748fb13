{"name": "odoo-js-config", "version": "1.0.0", "description": "JS Config for better DX in javascript", "scripts": {"format-all": "eslint --fix '**/*.js'", "lint-all": "eslint '**/*.js'", "lint-web": "eslint '**/web/**/*.js'", "format-staged": "lint-staged", "lint-diff": "echo '{\"extends\": [\"plugin:diff/diff\"]}' | eslint --resolve-plugins-relative-to . -c /dev/stdin '**/*.js'", "format-diff": "echo '{\"extends\": [\"plugin:diff/diff\"]}' | eslint --fix --resolve-plugins-relative-to . -c /dev/stdin '**/*.js'"}, "devDependencies": {"eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-diff": "^2.0.1", "eslint-plugin-prettier": "^4.2.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "@types/jquery": "^3.5.14", "@types/luxon": "^3.1.0", "@types/qunit": "^2.19.3", "@odoo/o-spreadsheet": "alpha", "@odoo/owl": "^2.0.1"}, "lint-staged": {"*.js": ["eslint --fix"]}, "engines": {"node": ">= 16.11.0"}}