<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.AnimatedNumber">
        <div class="o_animated_number ms-2 text-900 text-nowrap cursor-default" t-att-class="state.value !== props.value and props.animationClass" t-att-title="props.title">
            <t t-slot="prefix"/>
            <span t-if="props.currency and props.currency.position === 'before'" t-esc="props.currency.symbol" class="ms-1"/>
            <b t-esc="format(state.value)" />
            <span t-if="props.currency and props.currency.position === 'after'" t-esc="props.currency.symbol" class="ms-1"/>
        </div>
    </t>

</templates>
