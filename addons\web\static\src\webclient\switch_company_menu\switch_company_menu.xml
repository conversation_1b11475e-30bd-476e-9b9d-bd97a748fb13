<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.SwitchCompanyMenu">
    <div class="o_switch_company_menu d-none d-md-block">
        <DropdownGroup group="'web-navbar-group'">
            <Dropdown
                position="'bottom-end'"
                menuRef="containerRef"
                state="this.dropdown"
                onStateChanged.bind="handleDropdownChange"
                navigationOptions="navigationOptions"
                menuClass="'o_switch_company_menu_dropdown'"
                disabled="isSingleCompany"
            >
                <button data-hotkey="shift+u" t-att-disabled="isSingleCompany" t-att-title="companyService.currentCompany.name">
                    <i class="fa fa-building d-lg-none"/>
                    <span class="oe_topbar_name d-none d-lg-block text-truncate" t-esc="companyService.currentCompany.name"/>
                </button>

                <t t-set-slot="content">
                    <t t-call="web.SwitchCompanyMenu.Items"/>
                </t>
            </Dropdown>
        </DropdownGroup>
    </div>
</t>

<t t-name="web.SwitchCompanyMenu.Items">
    <div class="d-flex flex-row pe-2 mb-2" t-att-class="{ 'visually-hidden': !state.showFilter }">
        <div
            role="menuitemcheckbox"
            t-att-aria-checked="companySelector.selectedCompaniesIds.length > 0 ? 'true' : 'false'"
            t-att-aria-label="companySelector.selectedCompaniesIds.length > 0 ? 'Deselect all' : 'Select all'"
            t-att-title="companySelector.selectedCompaniesIds.length > 0 ? 'Deselect all' : 'Select all'"
            t-on-click="() => companySelector.selectAll()"
        >
            <span class="btn border-0 pt-2 px-2" t-att-class="selectAllClass">
                <i class="fa fa-fw" t-att-class="selectAllIcon"/>
            </span>
        </div>
        <div
            class="form-control d-flex align-items-center py-1"
            role="search"
        >
            <i class="oi oi-search me-2" role="img" aria-label="Search..." title="Search..."/>
            <div class="d-flex flex-grow-1 flex-wrap gap-1">
                <input
                    t-on-input="this.onSearch"
                    t-att-value="this.state.searchFilter"
                    t-ref="inputRef"
                    type="text"
                    class="o_input flex-grow-1 border-0 o-navigable"
                    placeholder="Search..."
                    role="searchbox"
                />
            </div>
        </div>
    </div>

    <div class="o_switch_company_menu_items">
        <t t-foreach="companiesEntries" t-as="entry" t-key="entry.company.id">
            <SwitchCompanyItem company="entry.company" level="entry.level"/>
        </t>
    </div>

    <div t-if="companySelector.hasSelectionChanged" class="o_switch_company_menu_buttons d-flex justify-content-end gap-2 px-2 pt-2">
        <button
            class="btn btn-primary o-navigable flex-grow-1 text-center"
            t-on-click="() => this.confirm()"
        >
            Confirm
        </button>
        <button
            class="btn btn-secondary o-navigable flex-grow-1 text-center"
            t-on-click="() => companySelector.reset()"
        >
            Reset
        </button>
    </div>
</t>

</templates>
