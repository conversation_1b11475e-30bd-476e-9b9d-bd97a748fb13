.o_export_data_dialog .modal-body {
    display: flex;

    .row {
        flex: 1;
    }

    .o_left_panel, .o_right_panel {

        .o_left_field_panel, .o_right_field_panel {
            user-select: none;

            .o_export_tree_item {
                &:hover:not(.o_expanded) {
                    background: rgba($o-brand-odoo, .7);
                    color: white;
                }

                &.o_expanded {
                    border-bottom: 1px solid grey;
                    background: rgba($o-brand-odoo, .2);
                }

                .o_expand_parent {
                    inset: 4px auto auto 0.5em;
                    line-height: inherit;
                }
            }

            .o_export_field_sortable > *:not(.o_remove_field) {
                @include o-grab-cursor;
            }
        }
    }
}
