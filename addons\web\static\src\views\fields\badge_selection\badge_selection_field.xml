<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <div t-name="web.BadgeSelectionField" class="d-flex flex-wrap gap-1 mb-3">
        <t t-if="props.readonly">
            <span t-esc="string" t-att-raw-value="value" />
        </t>
        <t t-else="">
            <t t-foreach="options" t-as="option" t-key="option[0]">
                <span
                    class="o_selection_badge btn btn-secondary mb-1"
                    t-att-class="{ 'active': value === option[0], 'btn-sm': props.size === 'sm', 'btn-lg': props.size === 'lg' }"
                    t-att-value="stringify(option[0])"
                    t-esc="option[1]"
                    t-on-click="() => this.onChange(option[0])"
                />
            </t>
        </t>
    </div>

</templates>
