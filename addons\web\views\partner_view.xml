<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.actions.server" id="download_contact">
        <field name="name">Download (vCard)</field>
        <field name="model_id" ref="model_res_partner"/>
        <field name="binding_model_id" ref="model_res_partner"/>
        <field name="binding_view_types">form,list</field>
        <field name="state">code</field>
        <field name="code">
            action = {
                'type': 'ir.actions.act_url',
                'url': '/web/partner/vcard?partner_ids=' + ','.join(map(str, records.ids)),
                'target': 'download',
            }
        </field>
    </record>
</odoo>
