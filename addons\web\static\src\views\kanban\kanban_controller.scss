// ------- Kanban Menu -------
.o-dropdown--kanban-record-menu {
    margin-top: -1px;
    margin-bottom: 0px;
    min-width: 9rem;

    // Records colours
    @include o-kanban-record-color;

    $o-kanban-manage-toggle-height: 35px;

    // Arbitrary value to place the dropdown-menu exactly below the
    // dropdown-toggle (height is forced so that it works on Firefox)
    top: $o-kanban-manage-toggle-height;

    > div {
        padding: 3px 0 3px 20px;
        visibility: visible;
        margin-bottom: 5px;
    }

    // Dropdown menu with complex layout
    .container {
        max-width: 400px;
        margin-left: 0;
        margin-right: 0;

        .row {
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            padding-left: $o-kanban-dashboard-dropdown-complex-gap*2;
            padding-right: $o-kanban-dashboard-dropdown-complex-gap*2;
        }

        div[class*="col-"] {
            flex: 1 1 percentage(1/3);
            padding-left: $o-kanban-dashboard-dropdown-complex-gap;
            padding-right: $o-kanban-dashboard-dropdown-complex-gap;
            max-width: none;

            > .o_kanban_card_manage_title {
                margin: (($font-size-base * $line-height-base) / 2) 0;
            }
            > div:not(.o_kanban_card_manage_title) {
                @include o-kanban-dashboard-dropdown-link($link-padding-gap: $o-kanban-dashboard-dropdown-complex-gap);
            }
        }

        .row.o_kanban_card_manage_settings {
            padding-top: $o-kanban-dashboard-dropdown-complex-gap*3;

            &:not(:first-child) {
                border-top: 1px solid $dropdown-divider-bg;
            }

            .o_kanban_colorpicker {
                max-width: none;
                padding: 0;
                margin-left: 2px !important;
            }

            div[class*="col-"] + div[class*="col-"] {
                border-left: 1px solid $dropdown-divider-bg;
            }
            > div:last-child {
                @include o-kanban-dashboard-dropdown-link($link-padding-gap: $o-kanban-dashboard-dropdown-complex-gap);
                padding-left: $dropdown-item-padding-x;
                padding-right: $dropdown-item-padding-x;
            }
        }
    }

}

// ------- Kanban View -------
.o_kanban_view {
    @include media-breakpoint-down(md) {
        @include media-only(screen) {
            --ControlPanel-border-bottom: none;
        }

        &.o_field_x2many_kanban .o_kanban_ghost {
            display: none;
        }
    }
}

// ------- Kanban renderer -------
.o_kanban_renderer {
    // ----------------------------------------------------------------------------
    // Default KanbanView values

    --Kanban-background: #{$o-kanban-background};
    --Kanban-gap: unset;
    --Kanban-padding: 0;

    --KanbanGroup-background: var(--Kanban-background);
    --KanbanGroup-padding-h: #{$o-kanban-group-padding};
    --KanbanGroup-padding-bottom: #{$o-kanban-group-padding};

    --KanbanRecord-width: #{$o-kanban-default-record-width};
    --KanbanRecord-margin-v: #{$o-kanban-record-margin};
    --KanbanRecord-margin-h: #{$o-kanban-record-margin};
    --KanbanRecord-padding-v: #{$o-kanban-inside-vgutter};
    --KanbanRecord-padding-h: #{$o-kanban-inside-hgutter};
    --KanbanRecord-gap-v: #{$o-kanban-inner-hmargin};

    --KanbanRecord--small-width: #{$o-kanban-small-record-width};

    --KanbanRecord__image-width: #{$o-kanban-image-width};
    --KanbanRecord__image--fill-width: #{$o-kanban-image-fill-width};

    --KanbanRecord__dropdown-gap: #{$border-width};

    --KanbanColumn__highlight-background: #{tint-color($o-action, 90%)};
    --KanbanColumn__highlight-border: #{$o-action};

    --o-view-nocontent-zindex: 1;

    --Card-Footer-gap: 4px;

    // ----------------------------------------------------------------------------
    // Inner components contextual adaptations

    --Ribbon-font-size: #{$font-size-sm};
    --Ribbon-gap-top: calc(var(--KanbanRecord-padding-v) * -1 - #{$border-width});
    --Ribbon-gap-right: 0;
    --Ribbon-wrapper-width: 6.5rem;
    --Ribbon-height:  calc(var(--Ribbon-font-size) * 2);

    // ----------------------------------------------------------------------------

    border-top: $border-width solid var(--Kanban-background);
    background: var(--Kanban-background);
    padding: var(--Kanban-padding);
    gap: var(--Kanban-gap);

    @include media-only(print) {
        --Kanban-gap: #{map-get($spacers, 2)};
        --KanbanGroup-padding-h: 0;
        --KanbanRecord-margin-h: 0;
    }

    @include media-only(print) {
        @include media-breakpoint-down(md) {
            --KanbanRecord-padding-h: #{$o-kanban-inside-hgutter * 2};
        }
    }

    .o_kanban_record:not(.o_legacy_kanban_record) {
        --fieldWidget-display: block;
        --Tag-max-width: 100%;
        --Tag-font-size: #{$o-font-size-base-smaller};
        --Tags-margin: calc(1em - var(--Tag-font-size, .75rem)) 0;

        position: relative;
        display: flex;
        align-items: stretch;
        min-width: 150px;
        margin: 0 0 (-$border-width);
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;

        &:first-of-type {
            margin-top: 1px;
        }

        // do not break wrap inside buttons
        button {
            word-wrap: normal;
            word-break: normal;
        }

        @include media-only(print) {
            font-size: 0.8rem;
        }

        &:not(.o_kanban_ghost) {
            padding: var(--KanbanRecord-padding-v) var(--KanbanRecord-padding-h);
            border: $border-width solid $border-color;
            background-color: $o-view-background-color;
        }

        &:where(:not(.row)) {
            flex-flow: column;

            main {
                flex-grow: 1;
            }

            aside {
                flex: 0 0 var(--KanbanRecord__image-width);
            }
        }

        > main {
            display: flex;
            flex-flow: column;
            min-width: 0;
            height: 100%;
        }

        > footer, > main > footer {
            display: flex;
            align-items: center;
            column-gap: var(--Card-Footer-gap, #{map-get($spacers, 2)});
            margin-top: auto;
            padding-top: var(--KanbanRecord-padding-v);
            font-size: var(--Card-Footer-font-size, .875rem);
        }

        aside {
            img {
                height: var(--KanbanRecord__image-height, var(--KanbanRecord__image-width));
            }

            &.o_kanban_aside_full {
                --KanbanRecord__image-height: 100%;
                --KanbanRecord__image-width: var(--KanbanRecord__image--fill-width);

                .o_kanban_image_fill {
                    min-height: var(--KanbanRecord__image-fill-width);
                    max-height: var(--KanbanRecord__image-max-height, $o-kanban-image-fill-width * 1.5);
                }

                > * {
                    @include media-breakpoint-up(md) {
                        height: calc(100% + var(--KanbanRecord-padding-v)* 2);
                        margin-top: calc(var(--KanbanRecord-padding-v)* -1);
                        margin-bottom: calc(var(--KanbanRecord-padding-v)* -1);
                        margin-left: calc(var(--KanbanRecord-padding-h)* -1);
                    }
                }
            }
        }

        &.o_kanban_global_click {
            cursor: pointer;
        }

        .o_dropdown_kanban {
            visibility: hidden;

            @include media-breakpoint-down(md) {
                visibility: visible;
            }
        }

        &:hover .o_dropdown_kanban,
        .o_dropdown_kanban:has(.show) {
            visibility: visible;
        }

        .o_attachment_image > img {
            width: 100%;
            height: auto;
            border-radius: var(--Attachment-Image-border-radius, #{$border-radius});
        }

        // Records colours
        @for $size from 2 through length($o-colors) {
            // Note: the first color is not defined as it is the 'no color' for kanban
            &.o_kanban_color_#{$size - 1}::after {
                $-color: nth($o-colors, $size);

                @include o-position-absolute(0, auto, 0, -$border-width);
                border-left: $border-width solid rgba($-color, 0.5);
                border-right: ($border-width * 2) solid $-color;
                content: "";
            }
        }
    }

    &.o_kanban_ungrouped .o_kanban_record:not(.o_legacy_kanban) {
        flex-grow: 1;
        @include media-breakpoint-down(md) {
            flex-shrink: 0;
        }
    }

    .o_kanban_quick_create {
        border: $border-width solid $border-color;
        background-color: $o-view-background-color;
    }

    .o_kanban_quick_create {
        margin-bottom: calc(var(--KanbanRecord-margin-h) * 2);
        margin-top: calc(var(--KanbanRecord-margin-h) * 2);
        padding: 8px;

        .o_form_view .o_inner_group {
            margin: 0;
        }
    }

    .o_dragged {
        transform: rotate(-3deg);
        transition: transform 0.6s, box-shadow 0.3s;

        .dropdown {
            display: none;
        }
    }

    // Kanban Grouped Layout
    &.o_kanban_grouped {
        --KanbanGroup-width: calc(var(--KanbanRecord--small-width) + (2 * var(--KanbanGroup-padding-h)));

        min-height: 100%;

        @include media-breakpoint-down(md) {
            --KanbanGroup-width: 90%; // don't take full width to give a hint of next/previous column on smaller screens

            min-height: initial;
            height: 100%;
            overflow: scroll hidden !important;
            scroll-snap-type: x mandatory;
        }

        @include media-breakpoint-up(lg) {
            --KanbanGroup-width: calc(var(--KanbanRecord-width) + (2 * var(--KanbanGroup-padding-h)));
        }

        .o_kanban_record {
            width: 100%;
        }

        .o_kanban_group {
            background: var(--KanbanGroup-background);
        }

        .o_kanban_group,
        .o_column_quick_create {
            flex-basis: var(--KanbanGroup-width);

            @include media-breakpoint-up(md) {
                max-width: var(--KanbanGroup-width);
            }
        }
    }

    // Kanban Grouped Layout - Column default
    .o_kanban_group {
        .o_kanban_header > .o_kanban_header_title {
            &:hover .o_kanban_config,
            .o_kanban_config.show {
                visibility: visible;
            }

            .o_kanban_config {
                visibility: hidden;
                @include media-breakpoint-down(md) {
                    visibility: visible;
                }
            }
        }

        .o_kanban_header {
            .o-dropdown--kanban-config-menu {
                cursor: default;
            }
        }

        &.o_kanban_hover {
            background-color: var(--KanbanColumn__highlight-background) !important;
            box-shadow: -1px 0px 0px 0px var(--KanbanColumn__highlight-border) inset,
                1px 0px 0px 0px var(--KanbanColumn__highlight-border) inset;

            .o_kanban_header {
                // hack to display correct background color
                margin: 0 calc(-1 * var(--KanbanGroup-padding-h) + 1px);
                padding: 0 calc(var(--KanbanGroup-padding-h) - 1px);
            }
        }
    }

    .o_kanban_group, .o_column_quick_create {
        padding: 0 var(--KanbanGroup-padding-h) var(--KanbanGroup-padding-bottom);
    }

    .o_kanban_group:not(.o_column_folded) .o_kanban_header, .o_quick_create_folded {
        background: inherit;
    }

    .o_kanban_group:not(.o_column_folded) .o_kanban_header {
        .dropdown-item {
            max-width: calc(var(--KanbanRecord-width) * 0.75);
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .o_quick_create_folded {
        top: map-get($spacers, 3);
    }

    .o_group_draggable .o_column_title {
        cursor: grab;

        &:active {
            cursor: grabbing;
        }
    }

    // Kanban Grouped Layout - Column Folded
    .o_kanban_group.o_column_folded {

        // don't fill the width of record for a folded column
        .o_kanban_record.o_draggable {
            display: none !important;
        }
        // don't visually fold on smaller screens (aka. mobile)
        @include media-breakpoint-up(md) {
            --KanbanGroup-padding-h: 0;

            &, .o_column_unfold {
                cursor: col-resize;
            }

            & + .o_kanban_group.o_column_folded {
                margin-left: 1px;
            }

            .o_kanban_header {
                padding: 0 floor($o-kanban-group-padding * 0.7);
            }

            button.o_column_unfold {
                padding: 0 .15rem;

                .fa-caret-left {
                    margin-right: 0.2rem;
                }
            }

            &:hover button.o_column_unfold {
                padding: 0;

                .fa-caret-left {
                    margin-right: 0.5rem;
                }
            }

            .o_column_title {
                @include o-position-absolute(map-get($spacers, 4));
                transform-origin: left bottom 0;
                transform: rotate(90deg);
                overflow: visible;
            }
        }
    }

    @include media-breakpoint-down(md) {
        .o_kanban_group,
        .o_column_quick_create {
            scroll-snap-align: center;
            overflow-y: scroll;
        }
    }

    // Kanban UN-grouped Layout
    &.o_kanban_ungrouped {
        min-height: 100%;

        @include media-breakpoint-up(md) {
            --Kanban-padding:  var(--KanbanRecord-margin-v) var(--KanbanRecord-margin-h);
        }

        .o_kanban_record {
            width: var(--KanbanRecord-width);
            margin: calc(var(--KanbanRecord-margin-v) * 0.5) var(--KanbanRecord-margin-h);

            @include media-breakpoint-down(md) {
                margin: 0 0 #{-$border-width};
                flex-basis: 100%;
            }

            &.o_kanban_ghost {
                max-height: 0 !important; // to prevent view writers to override this height
            }
        }
    }

    .o_field_monetary {
        /*rtl:ignore*/
        direction: ltr;
    }
}

.o_kanban_mobile .o_kanban_renderer .o_kanban_record {
    div.label {
        @include o-text-overflow;
    }
}

.o_kanban_small_column .o_kanban_renderer.o_kanban_grouped {
    @include media-breakpoint-up(md) {
        --KanbanGroup-width: calc(var(--KanbanRecord--small-width) + (2 * var(--KanbanGroup-padding-h)));
    }
}

// ------- Sample mode -------
.o_kanban_view .o_view_sample_data {
    // all records
    .o_kanban_record,
    // load more buttons
    .o_kanban_load_more,
    // progress bars and counters
    .o_kanban_counter {
        @include o-sample-data-disabled;
    }
}
