<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.KanbanColorPickerField">
        <ul t-if="!props.readonly" class="o_kanban_colorpicker mb-0 ms-2">
            <t t-foreach="colors" t-as="color" t-key="color_index">
                <li role="menuitem" t-on-click="() => this.selectColor(color_index)" t-att-title="color" t-att-aria-label="color">
                    <a href="#" t-attf-class="o_kanban_color_{{ color_index }}" />
                </li>
            </t>
        </ul>
    </t>

</templates>
