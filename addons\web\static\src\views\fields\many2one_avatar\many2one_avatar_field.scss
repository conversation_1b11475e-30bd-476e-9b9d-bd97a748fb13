.o_field_many2one_avatar {
    img:hover {
        cursor: context-menu;
        outline: $border-width solid $o-action;
    }
}

.o_kanban_record  {
    @include media-breakpoint-up(sm) {
        .o_field_widget.o_field_many2one_avatar_user .o_quick_assign {
            visibility: hidden;
        }
        &:hover {
            .o_field_widget.o_field_many2one_avatar_user .o_quick_assign {
                visibility: visible;
            }
        }
    }
}

.o_m2o_tags_avatar_field_popover {
    z-index: $zindex-modal - 1;
    font-size: $dropdown-font-size;
    > .o_field_many2one_selection {
        padding: 0.25rem;
        .o-autocomplete--dropdown-menu {
            width: 178px;
            font-size: $dropdown-font-size;
            .dropdown-item {
                padding: $o-dropdown-vpadding $o-dropdown-hpadding/2;
            }
        }
        .o-autocomplete--input {
            margin-bottom: .5rem;
            padding-bottom: 0.25rem;
        }
    }
}
