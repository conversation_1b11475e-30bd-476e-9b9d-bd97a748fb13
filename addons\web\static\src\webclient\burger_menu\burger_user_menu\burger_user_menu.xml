<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

  <t t-name="web.BurgerUserMenu">
    <div class="o_user_menu_mobile mt-2 px-3">
      <t t-foreach="getElements()" t-as="element" t-key="element_index">
          <t t-if="!element.hide">
              <a t-if="element.type == 'item'" class="d-block text-body py-3 fs-4" t-att-href="element.href or ''" t-out="element.description" t-on-click.stop.prevent="() => this._onItemClicked(element.callback)"/>
              <CheckBox
                  t-if="element.type == 'switch'"
                  value="element.isChecked"
                  className="'form-switch d-flex flex-row-reverse justify-content-between py-3 ps-0 fs-4 w-100'"
                  onChange="element.callback"
              >
                  <t t-out="element.description"/>
              </CheckBox>
              <hr t-if="element.type == 'separator'"/>
          </t>
      </t>
    </div>
  </t>

</templates>
