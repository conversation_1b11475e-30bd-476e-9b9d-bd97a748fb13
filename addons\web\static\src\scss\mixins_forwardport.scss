// For each breakpoint, define the maximum width of the container in a media query
@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {
    @each $breakpoint, $container-max-width in $max-widths {
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            max-width: $container-max-width;
        }
    }
}

//addons/web/static/lib/bootstrap-4/scss/mixins/_text-emphasis.scss
@mixin text-emphasis-variant($parent, $color) {
    #{$parent} {
        color: $color !important;
    }
    @if $link-shade-percentage != 0 {
        a#{$parent} {
            &:hover, &:focus {
                color: darken($color, $link-shade-percentage) !important;
            }
        }
    }
}
