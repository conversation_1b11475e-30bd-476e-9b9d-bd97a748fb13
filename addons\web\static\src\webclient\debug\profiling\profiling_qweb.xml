<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.ProfilingQwebView">
    <div class="oe_form_field o_ace_view_editor oe_ace_open o_profiling_qweb_view">
        <div class="o_select_view_profiling" t-ref="selector">
            <a role="button" class="dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" href="#"><MenuItem view="state.view" t-if="state.view"/></a>
            <div class="dropdown-menu" role="menu">
                <t t-foreach="viewObjects" t-as="view" t-key="view_index">
                    <a role="menuitem" href="#" t-att-data-id="view.id" t-on-click.prevent="_onSelectView">
                        <MenuItem view="view"/>
                    </a>
                </t>
            </div>
        </div>
        <div class="ace-view-editor" t-ref="ace"/>
        <small class="text-muted">
            It is possible that the "t-call" time does not correspond to the overall time of the
            template. Because the global time (in the drop down) does not take into account the
            duration which is not in the rendering (look for the template, read, inheritance,
            compilation...). During rendering, the global time also takes part of the time to make
            the profile as well as some part not logged in the function generated by the qweb.
        </small>
    </div>
</t>
<t t-name="web.ProfilingQwebView.menuitem">
    <div class="o_delay"><t t-if="props.view.delay" t-esc="props.view.delay"/> ms</div>
    <div class="o_query"><t t-if="props.view.delay" t-esc="props.view.query"/> query</div>
    <t t-esc="props.view.display_name"/>
    <div class="o_key text-muted">(<t t-esc="props.view.id"/>, <t t-esc="props.view.key"/>)</div>
</t>
<t t-name="web.ProfilingQwebView.hover">
    <div class="o_info o_detail">
        <div class="o_delay"><t t-esc="delay"/> <span>ms</span></div>
        <div class="o_query"><t t-esc="query"/> <span>query</span></div>
    </div>
</t>
<t t-name="web.ProfilingQwebView.info">
    <div class="o_info">
        <div t-if="displayDetail" class="o_more">
            <span>*</span>
            <table class="o_detail">
                <thead>
                    <tr><th></th><th>ms</th><th>query</th></tr>
                </thead>
                <tbody>
                    <tr t-foreach="groups" t-as="directive" t-key="directive_index">
                        <td><t t-esc="directive"/></td>
                        <td><t t-esc="groups[directive].delays.join(' ')"/></td>
                        <td><t t-esc="groups[directive].querys.join(' ')"/></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="o_delay"><t t-esc="delay"/></div>
        <div class="o_query"><t t-esc="query"/></div>
    </div>
</t>

</templates>
