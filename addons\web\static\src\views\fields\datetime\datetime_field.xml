<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.DateTimeField">
        <t t-set="showSeparator" t-value="shouldShowSeparator()" />
        <div class="d-flex gap-2 align-items-center" t-ref="root">
            <!-- Start date -->
            <t t-if="props.readonly">
                <span class="text-truncate" t-esc="getFormattedValue(0)" />
            </t>
            <t t-elif="!props.required and !props.alwaysRange and isEmpty(startDateField) and !isEmpty(endDateField)">
                <button
                    class="o_add_date o_add_start_date btn btn-secondary btn-sm w-100"
                    t-on-click="() => this.addDate(0)"
                >
                    <i class="fa fa-plus me-2" />
                    Add start date
                </button>
            </t>
            <t t-elif="props.required or props.alwaysRange or !isEmpty(startDateField) or startDateField === props.name">
                <input
                    t-ref="start-date"
                    type="text"
                    t-att-id="showSeparator ? props.endDateField and props.id : props.id"
                    class="o_input cursor-pointer"
                    autocomplete="off"
                    t-att-placeholder="props.placeholder"
                    t-att-data-field="startDateField"
                    t-on-input="onInput"
                />
                <span
                    t-if="props.warnFuture and isDateInTheFuture(0)"
                    class="fa fa-exclamation-triangle text-danger"
                    title="This date is on the future. Make sure it is what you expected."
                />
            </t>

            <!-- Separator -->
            <t t-if="showSeparator">
                <i class="fa fa-long-arrow-right" aria-label="Arrow icon" title="Arrow" />
            </t>

            <!-- End date -->
            <t t-if="endDateField">
                <t t-if="props.readonly">
                    <span class="text-truncate" t-esc="getFormattedValue(1)" />
                </t>
                <t t-elif="!props.required and !props.alwaysRange and !isEmpty(startDateField) and isEmpty(endDateField)">
                    <button
                        class="o_add_date o_add_end_date btn btn-secondary btn-sm w-100"
                        t-on-click="() => this.addDate(1)"
                    >
                        <i class="fa fa-plus me-2" />
                        Add end date
                    </button>
                </t>
                <t t-elif="props.required or props.alwaysRange or !isEmpty(endDateField) or endDateField === props.name">
                    <input
                        t-ref="end-date"
                        type="text"
                        t-att-id="props.startDateField and props.id"
                        class="o_input cursor-pointer"
                        autocomplete="off"
                        t-att-placeholder="props.placeholder"
                        t-att-data-field="endDateField"
                        t-on-input="onInput"
                    />
                    <span
                        t-if="props.warnFuture and isDateInTheFuture(1)"
                        class="fa fa-exclamation-triangle text-danger"
                        title="This date is on the future. Make sure it is what you expected."
                    />
                </t>
            </t>
        </div>
    </t>
</templates>
