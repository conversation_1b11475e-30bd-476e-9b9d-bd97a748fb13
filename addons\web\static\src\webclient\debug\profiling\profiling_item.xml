<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.DebugMenu.ProfilingItem">
        <DropdownItem class="o_debug_profiling_item_wrapper">
            <div class="o_debug_profiling_item d-flex justify-content-between">
                <div class="align-self-center">
                    <div t-if="profiling.state.isEnabled" class="alert alert-info py-2 me-3">Recording...</div>
                    <span class="o_profiling_switch">
                        <span class="form-check form-switch" t-on-click.stop.prevent="() => this.profiling.toggleProfiling()">
                            <input type="checkbox" class="form-check-input" id="enable_profiling" t-att-checked="profiling.state.isEnabled"/>
                            <label class="form-check-label">Enable profiling</label>
                        </span>
                    </span>
                    <t t-if="profiling.state.isEnabled">
                        <span class="o_profiling_switch form-check form-switch mt-2" t-on-click.stop.prevent="() => this.profiling.toggleCollector('sql')">
                            <input type="checkbox" class="form-check-input" id="profile_sql"
                                t-att-checked="profiling.isCollectorEnabled('sql')"/>
                            <label class="form-check-label" for="profile_sql">Record sql</label>
                        </span>
                        <span class="o_profiling_switch form-check form-switch mt-2" t-on-click.stop.prevent="() => this.profiling.toggleCollector('traces_async')">
                            <input type="checkbox" class="form-check-input" id="profile_traces_async"
                                t-att-checked="profiling.isCollectorEnabled('traces_async')"/>
                            <label class="form-check-label" for="profile_traces_async">Record traces</label>
                        </span>
                        <div t-if="profiling.isCollectorEnabled('traces_async')" class="input-group input-group-sm mt-2" t-on-click.stop.prevent="">
                            <div class="input-group-text">Interval</div>
                            <select class="profile_param form-select" t-on-change="(ev) => this.changeParam('traces_async_interval', ev)">
                                <t t-set="interval" t-value="profiling.state.params.traces_async_interval"/>
                                <option value="">Default</option>
                                <option value="0.001" t-att-selected="interval === '0.001'">0.001</option>
                                <option value="0.01" t-att-selected="interval === '0.01'">0.01</option>
                                <option value="0.1" t-att-selected="interval === '0.1'">0.1</option>
                                <option value="1" t-att-selected="interval === '1'">1</option>
                            </select>
                        </div>
                        <div class="input-group input-group-sm mt-2">
                            <div class="input-group-text">Entry Count</div>
                            <input type="number" class="form-control" t-on-click.stop.prevent="" t-on-change="(ev) => this.changeParam('entry_count_limit', ev)" t-att-value="profiling.state.params.entry_count_limit || '0'" placeholder="None"/>
                        </div>
                        <span t-if="profiling.isCollectorEnabled('sql') || profiling.isCollectorEnabled('traces_async')" class="o_profiling_switch form-check form-switch mt-2" t-on-click.stop.prevent="(ev) => this.toggleParam('execution_context_qweb', ev)">
                            <input type="checkbox" class="form-check-input" id="profile_execution_context_qweb"
                                t-att-checked="!!profiling.state.params.execution_context_qweb"/>
                            <label class="form-check-label" for="profile_execution_context_qweb">Add qweb directive context</label>
                        </span>
                        <span class="o_profiling_switch form-check form-switch mt-2" t-on-click.stop.prevent="() => this.profiling.toggleCollector('qweb')">
                            <input type="checkbox" class="form-check-input" id="profile_qweb"
                                t-att-checked="profiling.isCollectorEnabled('qweb')"/>
                            <label class="form-check-label" for="profile_qweb">Record qweb</label>
                        </span>
                    </t>
                </div>
                <a href="#" t-on-click.prevent="openProfiles" t-attf-class="btn btn-light align-self-baseline {{profiling.state.isEnabled ? 'py-2' : ''}}">
                    <i class="o_open_profiling oi oi-launch" />
                </a>
            </div>
        </DropdownItem>
    </t>

</templates>
