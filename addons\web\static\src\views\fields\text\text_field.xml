<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.TextField">
        <t t-if="props.readonly">
            <span t-esc="props.record.data[props.name] or ''" />
        </t>
        <t t-else="">
            <div t-ref="div">
                <textarea
                    class="o_input"
                    t-att-class="{
                        'o_field_translate': isTranslatable,
                        'o_field_placeholder': props.dynamicPlaceholder,
                    }"
                    t-att-id="props.id"
                    t-att-placeholder="props.placeholder"
                    t-att-rows="rowCount"
                    t-ref="textarea"
                    t-on-blur="onBlur"
                />
                <div class="o_field_input_buttons">
                    <TranslationButton
                        t-if="isTranslatable"
                        fieldName="props.name"
                        record="props.record"
                    />
                    <button t-if="props.dynamicPlaceholder"
                        class="btn m-0 py-0 px-2 fa fa-magic position-relative"
                        title="Insert Field"
                        t-on-click="onDynamicPlaceholderOpen"
                    />
                </div>
            </div>
        </t>
    </t>

</templates>
