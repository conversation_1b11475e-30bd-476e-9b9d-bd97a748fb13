<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="web.X2ManyFieldDialog">
    <Dialog t-props="dialogProps">
        <FormRenderer record="record" archInfo="archInfo"/>
        <t t-set-slot="footer">
            <t t-if="footerArchInfo">
                <FormRenderer record="record" archInfo="footerArchInfo">
                    <t t-set-slot="default_buttons">
                        <t t-call="web.X2ManyFieldDialogDefaultButtons"/>
                    </t>
                </FormRenderer>
            </t>
            <t t-else="">
                <t t-call="web.X2ManyFieldDialogDefaultButtons"/>
            </t>
        </t>
    </Dialog>
</t>

<t t-name="web.Many2XAutocomplete" >
    <div class="o_input_dropdown" t-ref="autocomplete_container">
        <input t-if="env.isSmall and props.dropdown"
            type="text"
            t-att-id="props.id"
            class="o_input"
            readonly=""
            autocomplete="off"
            t-att-placeholder="props.placeholder"
            t-att-value="props.value"
            t-on-click="onSearchMore"
            t-on-barcode-search="onBarcodeSearch"
        />
        <AutoComplete t-else=""
            value="props.value"
            id="props.id"
            placeholder="props.placeholder"
            sources="sources"
            autoSelect="props.autoSelect"
            onSelect.bind="onSelect"
            onInput.bind="onInput"
            onChange.bind="onChange"
            dropdown="props.dropdown"
            autofocus="props.autofocus"
            resetOnSelect="props.value === ''"
            onCancel.bind="onCancel"
        />
        <span class="o_dropdown_button" />
    </div>
</t>

<t t-name="web.AvatarMany2XAutocomplete">
    <span class="o_avatar_many2x_autocomplete o_avatar d-flex align-items-center">
        <img t-if="option.value" class="rounded me-1"
             t-attf-src="/web/image/{{option.resModel}}/{{option.value}}/avatar_128" />
        <t t-esc="option.label" />
    </span>
</t>

<t t-name="web.X2ManyFieldDialogDefaultButtons">
    <t t-if="record.isInEdition">
        <t t-if="canCreate">
            <button class="btn btn-primary o_form_button_save" t-on-click="save" data-hotkey="c">Save &amp; Close</button>
            <button class="btn btn-primary o_form_button_save_new" t-on-click="saveAndNew" data-hotkey="n">Save &amp; New</button>
        </t>
        <t t-else="">
            <button class="btn btn-primary o_form_button_save" t-on-click="save" data-hotkey="c">Save</button>
        </t>
        <button class="btn btn-secondary o_form_button_cancel" t-on-click="discard" data-hotkey="j">Discard</button>

        <t t-if="props.delete">
            <button class="btn btn-secondary o_btn_remove" t-on-click="remove" data-hotkey="k">
                <t t-if="props.deleteButtonLabel" t-out="props.deleteButtonLabel"/>
                <t t-else="">Remove</t>
            </button>
        </t>
    </t>
    <t t-else="">
        <button class="btn btn-primary o_form_button_cancel" t-on-click="() => this.props.close()" data-hotkey="j">Close</button>
    </t>
</t>

</templates>
