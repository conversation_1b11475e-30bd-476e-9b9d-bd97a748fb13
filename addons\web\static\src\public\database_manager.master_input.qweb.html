<t>
    <t t-set="set_master_pwd" t-value="set_master_pwd or False" />
    <t t-set="input_class" t-value="'form-control'" />

    <t t-if="insecure">
        <t t-if="set_master_pwd">
            <input type="hidden" name="master_pwd" class="form-control" value="admin"/>
        </t>
        <t t-else="">
            <div class="alert alert-warning">
                <p>Warning, your Odoo database manager is not protected. To secure it, we have generated the following master password for it:</p>
                <p style="text-align: center;"><strong class="generated_master_pwd"></strong></p>
                <p>You can change it below but be sure to remember it, it will be asked for future operations on databases.</p>
            </div>
            <t t-set="input_class" t-value="'form-control generated_master_pwd_input'" />
        </t>
    </t>
    <t t-if="not insecure or not set_master_pwd">
        <div class="row mb-3">
            <label for="master_pwd" class="col-md-4 col-form-label">Master Password</label>
            <div class="col-md-8">
                <div class="input-group">
                    <input name="master_pwd"
                        t-att-class="input_class"
                        required="required"
                        autofocus="autofocus"
                        type="password"
                        autocomplete="current-password" />
                    <span class="fa fa-eye o_little_eye input-group-text" aria-hidden="true" style="cursor: pointer;"></span>
                </div>
            </div>
        </div>
    </t>
</t>
