.o_field_cursor_disabled {
    cursor: not-allowed;
}

// .o_field_highlight is used in several types of view to force fields
// to be displayed with a bottom border even when not hovered (e.g. added
// by mobile detection, in several specific places such as spreadsheet or
// knowledge sidebars, settings view, kanban quick create (not a form view), etc.)
.o_field_highlight .o_field_widget .o_input, .o_field_highlight.o_field_widget .o_input {
    border-color: var(--o-input-border-color);
}

// field decoration classes (e.g. text-danger) are set on the field root node,
// but bootstrap contextual classes do not work on input/textarea, so we have to
// explicitely set their color to the one of their parent
.o_field_widget {
    input, textarea, select {
        color: inherit;
    }
}

//------------------------------------------------------------------------------
// Field States
//------------------------------------------------------------------------------

:root {
    @include print-variable(o-input-border-color, $o-form-lightsecondary);
    @include print-variable(o-caret-color, $input-color);
}

.o_required_modifier {
    @include print-variable(o-input-border-color, $o-input-border-required);
    @include print-variable(o-caret-color, $o-input-border-required);
}

.o_field_widget:focus-within {
    @include print-variable(o-input-border-color, $o-action);
    @include print-variable(o-caret-color, $o-action);
}

.o_field_invalid {
    @include print-variable(o-input-border-color, map-get($theme-colors, 'danger'));
    @include print-variable(o-caret-color, map-get($theme-colors, 'danger'));
}

//------------------------------------------------------------------------------
// Generic input
//------------------------------------------------------------------------------

.o_input {
    border:$input-border-width solid var(--o-input-border-color);
    border-width: 0 0 $input-border-width 0;
    padding: $o-input-padding-y $o-input-padding-x;
    background-color: var(--o-input-background-color, #{$input-bg});

    &:placeholder-shown {
        text-overflow: ellipsis;
    }

    // -- Nested o_input(s)
    .o_input {
        border: 0;
        padding: 0;
    }
}

// Invalid
.o_field_invalid:not(:focus-within) {
    &.o_input, > .o_input, &.o_form_label {
        color: o-text-color('danger');
    }

    &:not(.o_form_label) {
        --o-input-background-color: #{$o-input-invalid-bg};
    }
}

// Empty
.o_field_empty {
    display: none;
}

// Numbers
.o_field_number {
    white-space: nowrap;
}

//------------------------------------------------------------------------------
// Fields
//------------------------------------------------------------------------------

.o_field_widget {
    // Default display and alignment of widget and internal <input/>
    text-align: inherit;
    display: var(--fieldWidget-display, inline-block);
    margin-bottom: var(--fieldWidget-margin-bottom);

    textarea.o_input,
    input.o_input {
        display: inline-block;
        text-align: inherit;
    }

    // Dropdowns inputs
    .o_input_dropdown, .o_datepicker {
        .o_input, > input {
            padding-right: ($caret-width * 2.5) + $o-input-padding-x;
        }

        .o_dropdown_button, .o_datepicker_button {
            @include o-position-absolute($o-input-padding-y, $o-input-padding-x, $input-border-width);
            pointer-events: none;

            &:after {
                @include o-caret-down;
            }
        }
    }

    // Block fields
    &.note-editor, &.o_field_html, &.oe_form_field_text, &.o_field_domain, &.o_graph_linechart, &.o_graph_barchart {
        display: block;
    }

    // Flex fields (inline)
    &.o_field_many2one, &.o_field_radio, &.o_field_percent_pie, &.o_field_monetary, &.o_field_binary_file, &.o_field_float_percentage {
        display: inline-flex;
        > span, > button {
            flex: 0 0 auto;
        }
    }

    // Dropdowns input (many2one, ...)
    .o_input_dropdown {
        flex: 1 1 auto;
        position: relative;
        width: 100%;

        > input {
            cursor: pointer;

            &::-ms-clear {
                display: none;
            }
        }
    }

    // Text
    &.o_field_text, &.oe_form_field_text .oe_form_text_content {
        width: 100%;
        white-space: pre-wrap;
    }

    // Monetary
    &.o_field_monetary, &.o_field_float_percentage {
        &.o_input {
            align-items: baseline;

            > input {
                width: 100px;
                flex: 1 0 auto;
            }
        }
    }

    // Many2One
    &.o_field_many2one {
        flex-direction: column;
    }

    .o_field_many2one_selection {
        display: flex;
        width: 100%;
    }

    .o_external_button {
        padding-top: 0;
        padding-bottom: 0;
    }

    // Handle widget
    .o_row_handle {
        @include o-grab-cursor;
        color: #adb5bd;
        text-align: center;
        width: 100%;
        &:hover {
            color: #666666;
        }
    }

    // Radio buttons
    &.o_field_radio {
        @include media-breakpoint-down(md) {
            display: inline-block;
        }
        .o_radio_input {
            outline: none;
        }

        .o_radio_item {
            flex: 0 0 auto;
        }

        &.o_horizontal {
            .o_radio_item {
                margin-right: $o-form-spacing-unit * 2;
            }
        }

        &.o_vertical {
            flex-flow: column nowrap;
            .o_radio_item {
                margin-bottom: $o-form-spacing-unit;
            }
        }
    }

    // Percent pie
    &.o_field_percent_pie {
        align-items: center;
    }

    // Ace editor
    &.o_ace_view_editor {
        width: 100%;
        .ace-view-editor {
            height: 300px;
            padding: 0 1em;
        }
    }

    // Image
    &.o_field_image, &.o_field_image_url {
        position: relative;

        .o_form_image_controls {
            @include o-position-absolute(0, 0);
            width: 100%;

            color: white;
            background-color: $o-brand-primary;
            opacity: 0;
            transition: opacity ease 400ms;
            min-width: 35px;

            > button.fa {
                border: none;
                background-color: transparent;
            }

            > .fa {
                padding: 4px;
                margin: 5px;
                cursor: pointer;
            }
        }
        @include media-breakpoint-down(sm, $o-extra-grid-breakpoints) {
            .o_form_image_controls{
                position: initial;
                opacity: 1;
                > .fa{
                    width: 50%;
                    padding: 6px;
                    margin: 0px;
                    text-align: center;
                    &.o_select_file_button{
                        background: $o-brand-primary;
                    }
                    &.o_clear_file_button{
                        background: map-get($theme-colors, 'danger');
                    }
                }
            }
        }

        &:hover .o_form_image_controls {
            opacity: 0.8;
        }

        &.o_field_invalid > img {
            border: 1px solid map-get($theme-colors, 'danger');
        }
    }

    // Input loading/file
    .o_form_binary_progress, .o_hidden_input_file .o_input_file {
        display: none;
    }

    // Domain
    &.o_field_domain {
        > .o_field_domain_panel {
            margin-top: 8px;
        }
        &.o_inline_mode.o_edit_mode {
            position: relative;

            > .o_field_domain_panel {
                @include o-position-absolute(0, 0);
                margin-top: 0;
            }
        }
    }

    // PDF Viewer
    &.o_field_pdfviewer, &.o_field_pdf_viewer .o_pdfview_iframe {
        width: 100%;
        height: 450px;
        border: 0;
        margin-top: 10px;
    }

    // Copy to clipboard
    &.o_field_copy {
        position: relative;
        width: 100% !important;
        border-radius: 5px;
        border: 1px solid $primary;
        font-size: $font-size-sm;
        color: $o-brand-primary;
        font-weight: $badge-font-weight;
        text-align: center;
        padding-right: 6rem;
        word-break: break-word;
        .o_clipboard_button {
            @include o-position-absolute($top: 0, $right: 0);
            &.o_btn_text_copy {
                position: absolute;
                top: 0;
                right: 0;
            }
            &.o_btn_char_copy {
                height: 100%;
            }
        }
        &.o_field_copy_url {
            cursor: pointer;
            padding-left: 3px;
        }
    }

    & > .o_field_color {
        border: 2px solid rgba(0, 0, 0, 0.25);
        border-radius: 100px;
        width: 30px;
        height: 30px;
        margin: 0 5px 0 0;
        display: inline-block;
    }

}

span.o_field_copy:empty {
    border: none;
}

button.o_field_float_toggle {
    width: 100%;
    text-align: center;
}

// Selection fields
select.o_field_widget.o_input {
    padding: 1px;  // Other inputs get a 1px padding automatically added by the browsers but selects do not
}
