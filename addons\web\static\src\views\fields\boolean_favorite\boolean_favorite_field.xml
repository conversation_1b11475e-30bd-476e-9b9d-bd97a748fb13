<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.BooleanFavoriteField">
        <div class="o_favorite" t-on-click.prevent.stop="update">
            <a href="#" t-att-class="{'pe-none' : props.readonly}">
                <i t-att-class="iconClass" role="img" t-att-title="label" t-att-aria-label="label"/>
                <t t-if="!props.readonly and !props.noLabel" t-esc="label"/>
            </a>
        </div>
    </t>

</templates>
