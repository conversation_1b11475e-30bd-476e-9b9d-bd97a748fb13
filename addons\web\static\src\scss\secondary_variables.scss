$o-webclient-background-color: $o-gray-100 !default;

$o-list-footer-color: null !default;
$o-list-group-header-color: lighten($o-brand-lightsecondary, 10%) !default;

// UI custom colors for tags, kanban records' colors, ...)
// Note: the first value is the old BS3 gray-light value
$o-colors: #a2a2a2, #ee2d2d, #dc8534, #e8bb1d, #5794dd, #9f628f, #db8865,
           #41a9a2, #304be0, #ee2f8a, #61c36e, #9872e6 !default;

$o-colors-secondary: #aa4b6b, #30C381, #97743a, #F7CD1F, #4285F4, #8E24AA,
                     #D6145F, #173e43, #348F50, #AA3A38, #795548, #5e0231,
                     #6be585, #999966, #e9d362, #b56969, #bdc3c7, #649173,
                     #ea00ff, #ff0026, #8bcc00, #00bfaf, #006aff, #af00bf,
                     #bf001d, #bf6300, #8cff00, #00f2ff, #004ab3, #ff00d0,
                     #ffa600, #3acc00, #00b6bf, #0048ff, #bf7c00, #04ff00,
                     #00d0ff, #0036bf, #ff008c, #00bf49, #0092b3, #0004ff,
                     #b20062, #649173 !default;

// UI custom colors, complete list
$o-colors-complete: join(
    $o-colors,
    $o-colors-secondary
)!default;

$o-sheet-cancel-hpadding: $o-horizontal-padding !default;
$o-sheet-cancel-bpadding: $o-horizontal-padding !default;

// Computation of the total height of a single line,
// useful to visually center elements eg. in a list view.
$o-line-size: $o-line-height-base * $o-font-size-base !default;

// Form

// Safest for the next value would be map-get($container-max-widths, lg) as it
// is the minimal width of the default form view design for md/lg sizes
$o-form-sheet-min-width: 990px !default;
$o-form-separator-color: invert($o-view-background-color, 10%) !default;

$o-caret-down: url("data:image/svg+xml," +
"<svg xmlns='http://www.w3.org/2000/svg' width='7' height='4' viewBox='0 0 7 4'>" +
    "<polygon fill='%23#{str-slice(#{$o-main-text-color}, 2)}' points='3.5 4 7 0 0 0'/>" +
"</svg>");
