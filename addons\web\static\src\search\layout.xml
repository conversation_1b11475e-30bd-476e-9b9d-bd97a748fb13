<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.Layout">
        <t t-if="env.inDialog" t-portal="'#' + env.dialogId + ' .modal-footer'">
            <t t-slot="layout-buttons"/>
        </t>
        <t t-component="components.ControlPanel" slots="controlPanelSlots" t-if="props.display.controlPanel" display="props.display.controlPanel"/>
        <div t-ref="content" class="o_content" t-attf-class="{{props.className}}" t-att-class="{ 'o_component_with_search_panel': props.display.searchPanel }">
            <t t-component="components.SearchPanel" t-if="props.display.searchPanel"/>
            <t t-slot="default" contentRef="contentRef" />
        </div>
    </t>

</templates>
