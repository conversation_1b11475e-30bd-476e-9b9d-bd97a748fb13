<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.PropertiesField">
        <div t-ref="properties" class="row"
            t-att-class="{'d-none': propertiesList.length === 0 and !state.showAddButton}"
            t-att-columns="renderedColumnsCount">
            <t t-set="unfoldedSeparators" t-value="state.unfoldedSeparators"/>
            <t t-set="_groupedPropertiesList" t-value="groupedPropertiesList"/>
            <t t-foreach="groupedPropertiesList" t-as="propertiesListGroup" t-key="propertiesListGroup_index">
                <t t-set="foldable" t-value="propertiesListGroup.name and propertiesListGroup.title and propertiesListGroup.title.length"/>
                <t t-set="isFolded" t-value="propertiesListGroup.name and !unfoldedSeparators.includes(propertiesListGroup.name)"/>
                <div class="o_inner_group o_group col-lg-6 o_property_group"
                    t-att-property-name="propertiesListGroup.name || ''">
                    <div t-if="!propertiesListGroup.invisibleLabel"
                        class="o_field_property_label o_field_property_group_label d-flex flex-row w-100 mb-3 text-uppercase fw-bolder align-items-baseline pe-2"
                        t-att-class="{'invisible': propertiesListGroup.columnSeparator}"
                        t-on-click="() => this.onSeparatorClick(propertiesListGroup.name)">
                        <span t-if="propertiesListGroup.title" t-out="propertiesListGroup.title"/>
                        <i
                            t-if="state.canChangeDefinition and !props.readonly and !propertiesListGroup.columnSeparator"
                            class="o_field_property_open_popover fa fa-pencil ms-3"
                            t-on-click.prevent.stop="(event) => this.onPropertyEdit(event, propertiesListGroup.name)"/>
                        <i t-if="state.canChangeDefinition and !props.readonly"
                            class="oi oi-draggable ms-1 d-none d-sm-block"/>
                        <div t-if="foldable" class="ms-auto">
                            <i class="fa me-1" t-att-class="isFolded ? 'fa-caret-right' : 'fa-caret-down'"/>
                        </div>
                    </div>
                    <div
                        t-foreach="propertiesListGroup.elements"
                        t-as="propertyConfiguration"
                        t-key="propertyConfiguration.name"
                        class="o_property_field o_wrap_label"
                        t-att-class="{
                            'd-flex flex-row': !env.isSmall and !isFolded, 'o_property_folded': isFolded,
                            'mb-4': renderedColumnsCount === 1, 'mb-3': renderedColumnsCount !== 1
                        }"
                        t-att-property-name="propertyConfiguration.name">
                        <t t-set="domId" t-value="generateUniqueDomID()"/>
                        <label
                            class="o_field_property_label o_form_label text-break d-flex align-items-baseline mb-0"
                            t-att-class="{'w-auto': env.isSmall}"
                            t-att-for="domId">
                            <span
                                t-if="propertyConfiguration.string and propertyConfiguration.string.length"
                                t-out="propertyConfiguration.string"
                                t-att-class="{'o_property_field_highlight': state.movedPropertyName === propertyConfiguration.name}"/>
                            <i
                                t-else="" class="o_field_property_empty_label">
                                New Property
                            </i>
                            <i
                                t-if="state.canChangeDefinition and !props.readonly"
                                class="o_field_property_open_popover fa fa-pencil ms-2"
                                t-on-click="(event) => this.onPropertyEdit(event, propertyConfiguration.name)"/>
                            <i t-if="state.canChangeDefinition and !props.readonly"
                                class="oi oi-draggable ms-1 d-none d-sm-block"/>
                        </label>
                        <div class="o_property_field_value w-100">
                            <PropertyValue
                                id="domId"
                                canChangeDefinition="state.canChangeDefinition"
                                checkDefinitionWriteAccess.bind="checkDefinitionWriteAccess ? checkDefinitionWriteAccess : () => {}"
                                comodel="propertyConfiguration.comodel || ''"
                                context="props.context"
                                domain="propertyConfiguration.domain || '[]'"
                                readonly="props.readonly"
                                selection="propertyConfiguration.selection"
                                string="propertyConfiguration.string"
                                tags="propertyConfiguration.tags"
                                type="propertyConfiguration.type"
                                value="propertyConfiguration.value"
                                onChange.bind="(value) => this.onPropertyValueChange(propertyConfiguration.name, value)"
                                onTagsChange.bind="(newTags, newValue) => this.onTagsChange(propertyConfiguration.name, newTags, newValue)"
                            />
                        </div>
                    </div>
                    <div
                        t-if="propertiesListGroup_index === _groupedPropertiesList.length - 1 and state.showAddButton"
                        class="o_field_property_add d-none d-sm-block"
                        t-att-class="{'g-col-2': props.columns !== 1}">
                        <button
                            t-if="!props.readonly and state.canChangeDefinition and definitionRecordId"
                            class="btn btn-light text-muted text-break m-0"
                            t-on-click="onPropertyCreate">
                            <i class="fa fa-plus"/>
                            Add a Property
                        </button>
                    </div>
                </div>
            </t>
        </div>
    </t>
</templates>
