// WKHTMLTOPDF doesn't support custom properties, this file
// overrides the utilities used in the reports.

$utilities: map-merge(
    $utilities,
    (
        // Disable 'text-X' and 'bg-X' classes generation,
        // generated in boostrap_review_report instead.
        "color": null,
        "background-color": null,
        // Border utilities
        "border": map-merge(
            map-get($utilities, "border"),
            (
                values: map-merge(
                    map-get(map-get($utilities, "border"), "values"),
                    (null: $border-width solid $border-color),
                ),
            ),
        ),
        "border-top": map-merge(
            map-get($utilities, "border-top"),
                (
                    values: map-merge(
                        map-get(map-get($utilities, "border-top"), "values"),
                        (null: $border-width solid $border-color),
                    ),
                ),
        ),
        "border-end": map-merge(
            map-get($utilities, "border-end"),
                (
                    values: map-merge(
                        map-get(map-get($utilities, "border-end"), "values"),
                        (null: $border-width solid $border-color),
                    ),
                ),
        ),
        "border-bottom": map-merge(
            map-get($utilities, "border-bottom"),
                (
                    values: map-merge(
                        map-get(map-get($utilities, "border-bottom"), "values"),
                        (null: $border-width solid $border-color),
                    ),
                ),
        ),
        "border-start": map-merge(
            map-get($utilities, "border-start"),
            (
                values: map-merge(
                    map-get(map-get($utilities, "border-start"), "values"),
                    (null: $border-width solid $border-color),
                ),
            ),
        ),
    )
);
