<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.PdfViewerField">
        <t t-if="!props.readonly">
            <div class="o_form_pdf_controls d-flex gap-1">
                <t t-if="props.record.data[props.name]">
                    <FileUploader
                        acceptedFileExtensions="'.pdf'"
                        onUploaded.bind="onFileUploaded"
                    >
                        <t t-set-slot="toggler">
                            <button
                                class="btn btn-secondary fa fa-pencil o_select_file_button"
                                data-tooltip="Edit"
                                aria-label="Edit"
                            />
                        </t>
                        <button
                            class="btn btn-secondary fa fa-trash o_clear_file_button"
                            data-tooltip="Clear"
                            aria-label="Clear"
                            t-on-click="onFileRemove"
                        />
                    </FileUploader>
                </t>
                <t t-else="">
                    <label class="o_select_file_button btn btn-primary">
                        <FileUploader
                            acceptedFileExtensions="'.pdf'"
                            onUploaded.bind="onFileUploaded"
                        >
                            <t t-set-slot="toggler">
                                Upload your file
                            </t>
                        </FileUploader>
                    </label>
                </t>
            </div>
        </t>
        <t t-if="props.record.data[props.name]">
            <iframe class="o_pdfview_iframe"
                alt="PDF file"
                t-att-src="url"
                t-att-name="props.name"
                t-on-error="onLoadFailed"
            />
        </t>
    </t>

</templates>
