<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.CalendarMobileFilterPanel">
        <div class="o_other_calendar_panel d-flex align-items-center d-print-none" t-on-click="props.toggleSideBar">
            <div class="o_filter me-auto d-flex overflow-auto">
                <t t-foreach="props.model.filterSections" t-as="section" t-key="section.fieldName">
                    <t t-if="section.filters.length gt 0">

                        <t t-foreach="getSortedFilters(section)" t-as="filter" t-key="filter.value">
                            <span t-if="filter.active" class="d-flex align-items-center text-nowrap ms-1 me-2">
                                <i class="fa fa-fw" t-att-class="getFilterColor(filter) !== 'o_color_false' ? 'fa-circle ' + getFilterColor(filter) : 'fa-circle-o'"/>
                                <span class="ms-1 fw-bold text-nowrap" t-esc="filter.label" />
                            </span>
                        </t>
                    </t>
                </t>
            </div>
            <i t-attf-class="oi fa-fw oi-chevron-{{caretDirection}} ms-2" />
        </div>
    </t>

</templates>
