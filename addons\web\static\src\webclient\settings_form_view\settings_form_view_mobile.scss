@include media-breakpoint-down(md) {

    .o_base_settings_view .o_form_renderer {
        flex-flow: column nowrap;

        > .o_control_panel {
            @include o-webclient-padding($top: 10px, $bottom: 10px);
        }

        .o_setting_container {
            flex-flow: column nowrap;

            .settings_tab {
                flex: 0 0 $o-base-settings-mobile-tabs-height;
                flex-direction: row;
                position: relative;
                overflow: hidden;
                overflow-x: auto;
                padding: 0;
                border-bottom: 1px solid $border-color;

                .tab {
                    display: block;
                    width: auto;
                    height: $o-base-settings-mobile-tabs-height;
                    padding: $o-base-settings-mobile-tabs-height*0.25 $o-base-settings-mobile-tabs-height*0.4;
                    text-align: center;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: inherit;
                    transition: 0.2s all ease 0s;
                    transform: translate3d(0, 0, 0);

                    .app_name {
                        display: block;
                        white-space: nowrap;
                        text-transform: uppercase;
                    }

                    &:after {
                        content: '';
                        background: $o-brand-primary;
                        opacity: 0;
                        @include o-position-absolute(auto, 0, 0, 0);
                        width: 100%;
                        height: 3px;
                        transition: 0.2s all ease 0s;
                    }

                    &.current {
                        font-weight: bold;

                        // Reset default style for 'selected' tabs
                        box-shadow: none;
                        background: none;

                        &:after {
                            opacity: 1;
                        }
                    }
                }
            }
        }

    }
}
