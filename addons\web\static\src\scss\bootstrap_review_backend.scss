///
/// This file regroups the CSS rules made to fix/extend bootstrap in the backend
/// ==============================================================================


// Buttons
// ============================================================================
// Generating bootstrap color buttons was disabled (see import_bootstrap.scss).
// We do it ourself here generating each btn design type individually.

.btn {
    --#{$variable-prefix}btn-disabled-border-color: transparent;
}

// 1. Render buttons defined in '$o-btns-bs-override' and '$o-btns-bs-outline-override'
//    maps.
@each $-name, $-value in $o-btns-bs-override {
    .btn-#{$-name} {
        @include button-variant(
            o-safe-get($-value, background),
            o-safe-get($-value, border),
            o-safe-get($-value, color),
            o-safe-get($-value, hover-background),
            o-safe-get($-value, hover-border),
            o-safe-get($-value, hover-color),
            o-safe-get($-value, active-background),
            o-safe-get($-value, active-border),
            o-safe-get($-value, active-color),
        );
    }
}

@each $-name, $-value in $o-btns-bs-outline-override {
    .btn-outline-#{$-name} {
        @include button-variant(
            o-safe-get($-value, background),
            o-safe-get($-value, border),
            o-safe-get($-value, color),
            o-safe-get($-value, hover-background),
            o-safe-get($-value, hover-border),
            o-safe-get($-value, hover-color),
            o-safe-get($-value, active-background),
            o-safe-get($-value, active-border),
            o-safe-get($-value, active-color),
        );
    }
}

@each $-name, $-value in $o-btns-bs-override {
    .btn-fill-#{$-name} {
        @include button-variant(
            o-safe-get($-value, background),
            o-safe-get($-value, border),
            o-safe-get($-value, color),
            o-safe-get($-value, hover-background),
            o-safe-get($-value, hover-border),
            o-safe-get($-value, hover-color),
            o-safe-get($-value, active-background),
            o-safe-get($-value, active-border),
            o-safe-get($-value, active-color),
        );
    }
}

// 2. Render '$theme-colors' buttons excluding the ones already generated
//    using the custom maps.
@each $-name, $-color in $theme-colors {
    @if (not map-get($o-btns-bs-override, $-name)) {
        .btn-#{$-name} {
            @include button-variant($-color, $-color);
        }
    }
    @if (not map-get($o-btns-bs-outline-override, $-name)) {
        .btn-outline-#{$-name} {
            @include button-outline-variant($-color);
        }
    }
}

// 3. Render custom buttons.
@each $-name, $-color in $o-btn-custom {
    .btn-#{$-name} {
        @include button-variant($-color, $-color);
    }
}

// Button group
// ============================================================================
.btn-group {
    > :not(.btn-check:first-child) + .btn,
    > .btn-group:not(:first-child) {
        margin-left: var(--btn-group-gap, $btn-border-width);
    }
}

// Bootstrap '$theme-colors' text-x and bg-x classes generation
// ============================================================================
// Bootstrap's default behavior is to generate classes directly from its
// '$theme-colors' map. However, we have disabled this to allow our own
// approach.

// 'text-x' classes
// ----------------------------------------------------------------------------
// 1. To comply with WCAG standards, we need to use text-colors that are
//    different from the ones in the '$theme-colors' map. We achieve this by
//    creating a temporary map that overrides uncompliant colors with
//    '$o-theme-text-colors' ones.
$-o-theme-text-colors: map-merge($theme-colors, $o-theme-text-colors);

// 2. Use different colors for links and buttons when in conjunction with
//    'text-x' classes to keep consistency with previous versions of Bootstrap.
//    To do so we pass the values through the 'text-emphasis-variant' mixin.
@each $-name, $-value in $-o-theme-text-colors {
    @include text-emphasis-variant(".text-#{$-name}", $-value);
}

// 3. Adapt the behaviour of .btn-link buttons when in conjuction with contextual
//    classes (eg. text-warning). 'muted' is set as default text color, while
//    the contextual-class color will be used on ':hover'.
//    Note: For the custom classes, this is duplicated below where the classes are
//    generated with the o-gray
.btn-link {
    font-weight: $btn-font-weight;

    @each $-name, $-color in $-o-theme-text-colors{
        &.btn-#{$-name}, &.text-#{$-name} {
            @include o-btn-link-variant($o-gray-600!important, o-text-color($-name) or $-color!important);
        }
    }
}

// 3. Cleanup temporary map
$-o-theme-text-colors: null;

// 'bg-x' classes
// ----------------------------------------------------------------------------
// 1. Generate $theme-colors classes using 'o-print-color()' mixin in order to
//    assign the color and print relative CSS variables as well. Note that
//    'black' and 'white' are not included in the map, we'll generate these
//    classes later in conjunction with legacy 'bg-white-x' and 'bg-black-x'
//    ones.
@each $-name, $-color in $theme-colors {
    .bg-#{$-name} {
        @include o-print-color($-color, background-color, bg-opacity);
    }
}


// Bootstrap black/white text-x and bg-x classes + legacy opacity ones
// ============================================================================

// 1. Define placeholder-selectors (aka pseudo-classes) for both white and
//    black. We'll then extend placeholder rather than classes to avoid
//    performance issues during SCSS compilation.
%-text-white {
    @include o-print-color($o-white, color, text-opacity);
}

%-text-black {
    @include o-print-color($o-black, color, text-opacity);
}

%-bg-white {
    @include o-print-color($o-white, background-color, bg-opacity);
}

%-bg-black {
    @include o-print-color($o-black, background-color, bg-opacity);
}

.text-white {
    @extend %-text-white;
}

.text-black {
    @extend %-text-black;
}

.bg-white {
    @extend %-bg-white;
}

.bg-black {
    @extend %-bg-black;
}

.bg-transparent{
    --bg-opacity: 0;
    @extend %-bg-black;
}

// 2. Generate legacy opacity-variations classes for both white and black. We
//    don't specifically assign colors here, we just extend placeholder
//    selectors. To control opacity we act on the '--X-opacity' Bootstrap
//    default CSS variables.
$-o-grayscale-opacities: map-remove($o-opacities, 0, 100);
@each $-name, $-value in $-o-grayscale-opacities {
    .bg-white-#{$-name} {
        --bg-opacity: #{$-value};
        @extend %-bg-white;
    }

    .bg-black-#{$-name} {
        --bg-opacity: #{$-value};
        @extend %-bg-black;
    }
}

@each $-name, $-value in $-o-grayscale-opacities {
    .text-white-#{$-name} {
        --text-opacity: #{$-value};
        @extend %-text-white;
    }

    .text-black-#{$-name} {
        --text-opacity: #{$-value};
        @extend %-text-black;
    }
}

// 3. Cleanup temporary map
$-o-grayscale-opacities: null;


// Odoo custom text/bg colors classes generation
// ============================================================================

// 1. Extend odoo's custom colors maps adding Bootstrap's grays.
//    Note: the card-body rule below needs those grays utilities to be defined
//    before, so that the related o-bg-color text-muted rules work.
$-o-text-colors-custom: map-merge($o-text-colors-custom, $o-grays);
$-o-bg-colors-custom: map-merge($o-bg-colors-custom, $o-grays);

// 2. Generate custom 'text-x' contextual classes
@each $-name, $-color in $-o-text-colors-custom {
    @include text-emphasis-variant(".text-#{$-name}", $-color);
}

// 3. Generate custom 'bg-x' and 'text-bg-x' contextual classes
@each $-name, $-color in map-merge($-o-bg-colors-custom, $o-grays) {
    .bg-#{$-name}, .text-bg-#{$-name}  {
        @include o-print-color($-color, background-color, bg-opacity);
    }

    .text-bg-#{$-name} {
        @include o-print-color(color-contrast($-color), color, text-opacity);
    }
}

// 4. Cleanup temporary maps
$-o-text-colors-custom: null;
$-o-bg-colors-custom: null;

// 5. Adapt the behaviour of .btn-link buttons when in conjuction with the custom
//    contextual classes (eg. text-action). 'muted' is set as default text color,
//    while the contextual-class color will be used on ':hover'.
//    Note: the same is done above for the '$theme-colors'. Here on the
//    '$o-text-colors-custom' because we don't want it to apply on the '$o-grays'.
.btn-link {
    @each $-name, $-color in $o-text-colors-custom{
        &.btn-#{$-name}, &.text-#{$-name} {
            @include o-btn-link-variant($o-gray-600!important, o-text-color($-name) or $-color!important);
        }
    }
}

// 6. Adds a bg-inherit custom utility class
.bg-inherit {
    background-color: inherit;
}

// Alert
// ==============================================================================
.alert {
    padding-top: var(--alert-padding-y, $alert-padding-y);
    padding-bottom: var(--alert-padding-y, $alert-padding-y);
    margin-bottom: var(--alert-margin-bottom, $alert-margin-bottom);
}

// Switches
// ============================================================================
.form-switch {
    .form-check-input:checked {
        background-color: $o-success;
        border-color: $o-success;
    }

    .form-check-input:focus {
        border-color: $o-success;
    }

    &:hover .form-check-input:not(:disabled) {
        border-color: $o-success;
    }

    // Creating a different styling for toggle switches with a purpose of switching
    // from A to B (eg. switching between month and year) rather than ON/OFF.
    // The 'o_switch_toggle' class should be use in addition to the default
    // 'form-switch' Bootstrap class.
    &.o_switch_toggle {
        .form-check-input, .form-check-input:checked {
            border-color: $o-action;
            background-color: $o-view-background-color;
            
            &, &:focus, &:checked {
                background-image: escape-svg(url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$o-action}'/></svg>"));
            }
        }
        
        &:hover .form-check-input:not(:disabled) {
            border-color: $o-action;
        }
    }

    &:disabled, &.disabled, &[disabled] {
        opacity: $o-opacity-disabled;
    }
}

// Switches
// ============================================================================
.table {
    > :not(caption) > * > * {
        color: unset;
    }
}
