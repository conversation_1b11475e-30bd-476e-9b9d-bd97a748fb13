<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.ProgressBarField">
        <div class="o_progressbar w-100 d-flex align-items-center" t-ref="numpadDecimal">
            <div t-if="props.title" class="o_progressbar_title text-nowrap pe-1"><t t-esc="props.title"/></div>
            <div class="o_progress align-middle overflow-hidden" aria-valuemin="0" t-att-aria-valuemax="this.maxValue" t-att-aria-valuenow="this.currentValue">
                <div t-attf-class="{{ progressBarColorClass }} h-100" t-att-style="'width: min(' + 100 * this.currentValue / this.maxValue + '%, 100%)'"></div>
            </div>
            <div class="o_progressbar_value d-flex">
                <t t-if="isPercentage">
                    <input
                        t-if="isEditable and props.isCurrentValueEditable"
                        t-ref="currentValue"
                        class="o_input h-100 text-center"
                        type="text"
                        inputmode="decimal"
                        autocomplete="off"
                        t-att-value="formatCurrentValue() or ''"
                        t-att-required="props.required"
                        t-on-change="onCurrentValueChange"
                        t-on-focus="onInputFocus"
                        t-on-blur="onInputBlur"
                    />
                    <span t-else="" class="mx-1" t-esc="formatCurrentValue(true)"/>
                    <span>%</span>
                </t>
                <t t-else="">
                    <input
                        t-if="isEditable and props.isCurrentValueEditable"
                        t-ref="currentValue"
                        class="o_input h-100 text-center"
                        type="text"
                        inputmode="decimal"
                        autocomplete="off"
                        t-att-value="formatCurrentValue()"
                        t-on-change="onCurrentValueChange"
                        t-on-focus="onInputFocus"
                        t-on-blur="onInputBlur"
                    />
                    <span t-else="" class="mx-1" t-esc="formatCurrentValue(true)"/>
                    /
                    <input
                        t-if="isEditable and props.isMaxValueEditable"
                        t-ref="maxValue"
                        class="o_input h-100 text-center"
                        type="text"
                        inputmode="decimal"
                        autocomplete="off"
                        t-att-value="formatMaxValue()"
                        t-on-change="onMaxValueChange"
                        t-on-focus="onInputFocus"
                        t-on-blur="onInputBlur"
                    />
                    <span t-else="" class="mx-1" t-esc="formatMaxValue(true)"/>
                </t>
            </div>
        </div>
    </t>

</templates>
