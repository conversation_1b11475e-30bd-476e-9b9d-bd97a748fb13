<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.SelectCreateDialog">
        <Dialog title="props.title" withBodyPadding="false">
            <t t-set-slot="header" t-slot-scope="scope">
                <t t-call="web.Dialog.header">
                    <t t-set="dismiss" t-value="scope.close"/>
                    <t t-set="fullscreen" t-value="scope.isFullscreen"/>
                </t>
                <button t-if="this.canUnselect" class="btn o_clear_button" t-on-click="() => this.unselect()">Clear</button>
            </t>
            <View t-props="viewProps" />
            <t t-set-slot="footer">
                <t t-if="props.multiSelect">
                    <button class="btn btn-primary o_select_button"
                        t-att-disabled="state.resIds.length === 0"
                        data-hotkey="v"
                        t-on-click="() => this.select(state.resIds)"
                    >
                        Select
                    </button>
                </t>
                <t t-if="!props.noCreate">
                    <button class="btn btn-primary o_create_button" data-hotkey="c" t-on-click="createEditRecord">New</button>
                </t>
                <button class="btn o_form_button_cancel" t-att-class="(!props.multiSelect &amp;&amp; props.noCreate) ? 'btn-primary' : 'btn-secondary'" data-hotkey="z" t-on-click="() => this.props.close()">Close</button>
            </t>
        </Dialog>
    </t>

    <t t-name="web.SelectCreateDialog.DefaultNoContentHelp">
        <p>No record found</p>
        <p>Adjust your filters or create a new record.</p>
    </t>

</templates>
