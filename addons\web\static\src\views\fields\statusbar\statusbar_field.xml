<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.StatusBarField.Dropdown">
        <t t-foreach="items" t-as="item" t-key="item.value">
            <DropdownItem
                class="getDropdownItemClassNames(item)"
                onSelected="() => this.selectItem(item)"
            >
                <span t-esc="item.label" />
            </DropdownItem>
        </t>
    </t>

    <t t-name="web.StatusBarField">
        <div class="o_statusbar_status" t-ref="root" role="radiogroup" aria-label="Statusbar">
            <Dropdown position="'bottom-end'">
                <button
                    t-ref="after"
                    type="button"
                    class="btn btn-secondary dropdown-toggle o_arrow_button o_first"
                    t-att-disabled="props.isDisabled"
                    aria-label="More..."
                >
                    ...
                </button>
                <t t-set-slot="content">
                    <t t-call="web.StatusBarField.Dropdown">
                        <t t-set="items" t-value="items.after" />
                    </t>
                </t>
            </Dropdown>
            <t t-foreach="items.inline" t-as="item" t-key="item.value">
                <button
                    type="button"
                    class="btn btn-secondary o_arrow_button"
                    t-att-class="{
                        o_first: item_first,
                        o_arrow_button_current: item.isSelected,
                        o_last: item_last,
                    }"
                    t-att-disabled="props.isDisabled || item.isSelected"
                    role="radio"
                    t-att-aria-checked="item.isSelected.toString()"
                    t-att-aria-current="item.isSelected and 'step'"
                    t-att-data-value="item.value"
                    t-esc="item.label"
                    t-on-click="() => this.selectItem(item)"
                />
            </t>
            <Dropdown position="'bottom-start'">
                <button
                    t-ref="before"
                    type="button"
                    class="btn btn-secondary dropdown-toggle o_arrow_button o_last"
                    t-att-disabled="props.isDisabled"
                    aria-label="More..."
                >
                    ...
                </button>
                <t t-set-slot="content">
                    <t t-call="web.StatusBarField.Dropdown">
                        <t t-set="items" t-value="items.before" />
                    </t>
                </t>
            </Dropdown>
            <Dropdown position="'bottom-end'">
                <button
                    t-ref="dropdown" 
                    type="button" 
                    class="btn btn-secondary dropdown-toggle"
                    t-att-disabled="props.isDisabled"
                >
                    <t t-out="getCurrentLabel()" />
                </button>
                <t t-set-slot="content">
                    <t t-call="web.StatusBarField.Dropdown">
                        <t t-set="items" t-value="getAllItems()" />
                    </t>
                </t>
            </Dropdown>
        </div>
    </t>
</templates>
