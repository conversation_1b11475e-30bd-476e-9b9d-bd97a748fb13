@mixin o-kanban-css-filter($class, $accent-color) {
    // Provide CSS reordering and highlight
    &.o_kanban_group_show_#{$class} {
        --KanbanGroup-background: #{mix($accent-color, $o-kanban-background, 5%)};

        outline: $border-width solid $o-view-background-color;
        box-shadow: inset 2px 0 0 $accent-color, inset -2px 0 0 $accent-color;

        .oe_kanban_card_#{$class} {
            order: 1;
        }

        .o_kanban_load_more {
            order: 2;
            padding: var(--KanbanRecord-padding-v) 0;
        }

        .o_kanban_record:not(.oe_kanban_card_#{$class}) {
            order: 3;
            @include o-hover-opacity(0.5);
        }
    }
}

.o_kanban_renderer {

    .o_kanban_counter {
        transition: opacity 0.3s ease 0s;

        > .o_column_progress {
            height: $font-size-sm;

            .bg-200 {
                --background-color: RGBA(#{to-rgb($o-gray-300)}, var(--bg-opacity, 1))
            }
        }
    }

    .o_kanban_group {
        @include o-kanban-css-filter(success, map-get($theme-colors, "success"));
        @include o-kanban-css-filter(warning, map-get($theme-colors, "warning"));
        @include o-kanban-css-filter(danger, map-get($theme-colors, "danger"));
        @include o-kanban-css-filter(muted, map-get($theme-colors, "dark"));
        @include o-kanban-css-filter(info, map-get($theme-colors, "info"));
        @include o-kanban-css-filter(purple, purple);
    }
}
