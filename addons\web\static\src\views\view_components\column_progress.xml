<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.ColumnProgress">
        <div class="o_column_progress progress bg-300 w-75">
            <t t-set="maxWidth" t-value="100 - Math.max(0, props.progressBar.bars.filter(x => x.count > 0).length - 1) * 5"/>
            <t t-foreach="props.progressBar.bars" t-as="bar" t-key="bar.value">
                <t t-set="progressWidth" t-value="Math.max(5, bar.count / (props.group.count or 1) * 100)"/>
                <div t-if="bar.count > 0"
                    role="progressbar"
                    class="progress-bar o_bar_has_records cursor-pointer"
                    t-att-class="{ 'progress-bar-animated progress-bar-striped': props.progressBar.activeBar === bar.value, 'border border-white': !props.group.isFolded and props.progressBar.activeBar }"
                    t-attf-class="bg-{{ bar.color }}"
                    t-attf-style="width: {{ Math.min(progressWidth, maxWidth) }}%;"
                    aria-valuemin="0"
                    t-att-aria-valuemax="props.group.count"
                    t-att-aria-valuenow="bar.count"
                    aria-label="Progress bar"
                    t-attf-data-tooltip="{{ bar.count }} {{ bar.string }}"
                    data-tooltip-delay="0"
                    t-on-click="() => this.onBarClick(bar)"
                />
            </t>
        </div>
        <AnimatedNumber
            value="props.aggregate.value"
            title="props.aggregate.title"
            duration="1000"
            currency="props.aggregate.currency"
            animationClass="props.aggregate.value > 999 ? 'o_animated_grow' : 'o_animated_grow_huge'"
        />
    </t>

</templates>