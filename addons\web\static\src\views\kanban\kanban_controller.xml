<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.KanbanView">
        <div t-att-class="className" t-ref="root">
            <Layout className="model.useSampleModel ? 'o_view_sample_data' : ''" display="props.display">
                <t t-set-slot="control-panel-create-button">
                    <t t-if="canCreate and props.showButtons">
                        <button type="button" class="btn btn-primary o-kanban-button-new" accesskey="c" t-on-click="() => this.createRecord()" data-bounce-button="">
                            New
                        </button>
                    </t>
                </t>
                <t t-set-slot="layout-buttons">
                    <t t-call="{{ props.buttonTemplate }}"/>
                </t>
                <t t-set-slot="control-panel-always-buttons">
                    <t t-foreach="headerButtons" t-as="button" t-key="button.id" t-if="!evalViewModifier(button.invisible)">
                        <MultiRecordViewButton
                            t-if="button.display === 'always'"
                            list="model.root"
                            className="button.className"
                            clickParams="button.clickParams"
                            defaultRank="'btn-secondary'"
                            domain="props.domain"
                            icon="button.icon"
                            string="button.string"
                            title="button.title"
                            attrs="button.attrs"
                        />
                    </t>
                </t>
                <t t-set-slot="control-panel-additional-actions">
                    <CogMenu/>
                </t>
                <t t-set-slot="layout-actions">
                    <SearchBar toggler="searchBarToggler"/>
                </t>
                <t t-set-slot="control-panel-navigation-additional">
                    <t t-component="searchBarToggler.component" t-props="searchBarToggler.props"/>
                </t>
                <t t-component="props.Renderer"
                    list="model.root"
                    archInfo="props.archInfo"
                    Compiler="props.Compiler"
                    readonly="true"
                    forceGlobalClick="props.forceGlobalClick"
                    deleteRecord.bind="deleteRecord"
                    openRecord.bind="openRecord"
                    noContentHelp="props.info.noContentHelp"
                    scrollTop.bind="scrollTop"
                    canQuickCreate="canQuickCreate"
                    quickCreateState="quickCreateState"
                    progressBarState="progressBarState"
                />
            </Layout>
        </div>
    </t>

    <t t-name="web.KanbanView.Buttons">
        <div t-if="props.showButtons" class="o_cp_buttons d-empty-none d-flex align-items-baseline gap-1" role="toolbar" aria-label="Main actions"/>
    </t>

</templates>
