// ------------------------------------------------------------------
// Base layout rules, use the 'webclient.scss' file for styling
// ------------------------------------------------------------------

%o-html-layout-fill {
  height: 100%;

  .o_web_client {
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
    overflow: hidden;

    > .o_action_manager {
      direction: ltr; //Define direction attribute here so when rtlcss preprocessor run, it converts it to rtl
      flex: 1 1 auto;
      height: 100%;
      overflow: hidden;

      > .o_action {
        height: 100%;
        display: flex;
        flex-flow: column nowrap;
        overflow: hidden;

        @include media-breakpoint-up(lg) {
          -ms-overflow-style: none; // IE and Edge
          scrollbar-width: none;    // Firefox

          &::-webkit-scrollbar {    // Chrome, Safari and Opera
            display: none;
          }
        }

        > .o_control_panel {
          flex: 0 0 auto;
        }
        .o_content {
          overflow: auto;
          height: 100%;
        }

        @include media-breakpoint-down(md) {
          // Made the o_action scroll instead of its o_content.
          // Except when the view wants to handle the scroll itself.
          &:not(.o_action_delegate_scroll), .o_form_view_container {
            overflow: auto;

            .o_content {
              overflow: initial;
            }
          }
        }
      }
    }

    .o_main_navbar {
      flex: 0 0 auto;
    }

    .o_control_panel {
      flex: 0 0 auto;
    }

    .o_content {
      direction: ltr; //Define direction attribute here so when rtlcss preprocessor run, it converts it to rtl
      flex: 1 1 auto;
      position: relative; // Allow to redistribute the 100% height to its child

      > .o_view_controller {
        position: absolute; // Get the 100% height of its flex parent
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 100%;
        direction: ltr;
      }
    }
  }

  // FIXME: Fix scrolling in modal on focus input due to a bug between Chrome (Android) and Bootstrap
  body.modal-open {
    position: fixed;
  }
}

@include media-only(screen) {
    html {
        @extend %o-html-layout-fill;
    }
}

//------------------------------------------------------------------------------
// Print Layout
//------------------------------------------------------------------------------

@include media-only(print) {
    .o_web_client {
        max-width: 100%;
    }

    @page {
        margin: 0.5cm;
        size: a4 auto;
    }
}
