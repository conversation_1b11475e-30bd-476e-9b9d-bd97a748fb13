<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.SearchBarMenu">
        <Dropdown menuClass="'o_search_bar_menu d-flex flex-wrap flex-lg-nowrap w-100 w-md-auto mx-md-auto mt-2 py-3'"
                  position="'bottom-end'"
                  state="this.props.dropdownState"
                  t-if="this.env.searchModel.searchMenuTypes.size">
            <button
                class="o_searchview_dropdown_toggler d-print-none btn btn-outline-secondary o-dropdown-caret rounded-start-0"
                data-hotkey="shift+q"
                title="Toggle Search Panel"
            >
            </button>
            <t t-set-slot="content">
                <!-- Filter -->
                <t t-if="this.env.searchModel.searchMenuTypes.has('filter')">
                    <div class="o_dropdown_container o_filter_menu w-100 w-lg-auto h-100 px-3 mb-4 mb-lg-0 border-end">
                        <div class="px-3 fs-5 mb-2">
                            <i class="me-2 text-primary" t-att-class="facet_icons.filter"/>
                            <h5 class="o_dropdown_title d-inline">Filters</h5>
                        </div>
                        <t t-set="currentGroup" t-value="null"/>
                        <t t-foreach="filterItems" t-as="item" t-key="item.id">
                            <t t-if="currentGroup !== null and currentGroup !== item.groupNumber">
                                <div class="dropdown-divider" role="separator"/>
                            </t>
                            <t t-if="item.options">
                                <AccordionItem class="'text-truncate'" description="item.description" selected="item.isActive">
                                    <t t-set="subGroup" t-value="null"/>
                                    <t t-foreach="item.options" t-as="option" t-key="option.id">
                                        <t t-if="subGroup !== null and subGroup !== option.groupNumber">
                                            <div class="dropdown-divider" role="separator"/>
                                        </t>
                                        <CheckboxItem class="{ o_item_option: true, selected: option.isActive }"
                                                            t-esc="option.description"
                                                            checked="option.isActive"
                                                            closingMode="'none'"
                                                            onSelected="() => this.onFilterSelected({ itemId: item.id, optionId: option.id })"
                                        />
                                        <t t-set="subGroup" t-value="option.groupNumber"/>
                                    </t>
                                </AccordionItem>
                            </t>
                            <t t-else="">
                                <CheckboxItem class="{ 'o_menu_item text-truncate': true, selected: item.isActive }"
                                                    checked="item.isActive"
                                                    closingMode="'none'"
                                                    t-esc="item.description"
                                                    attrs="{ title: item.description.length > 15 ? item.description : ''}"
                                                    onSelected="() => this.onFilterSelected({ itemId: item.id })"
                                />
                            </t>
                            <t t-set="currentGroup" t-value="item.groupNumber"/>
                        </t>
                        <t t-if="filterItems.length">
                            <div role="separator" class="dropdown-divider"/>
                        </t>
                        <DropdownItem class="'o_menu_item o_add_custom_filter'" onSelected.bind="onAddCustomFilterClick">Add Custom Filter</DropdownItem>
                    </div>
                </t>
                <!-- GroupBy -->
                <t t-if="this.env.searchModel.searchMenuTypes.has('groupBy')">
                    <div class="o_dropdown_container o_group_by_menu w-100 w-lg-auto h-100 px-3 mb-4 mb-lg-0 border-end">
                        <div class="px-3 fs-5 mb-2">
                            <i class="me-2 text-action" t-att-class="facet_icons.groupBy"/>
                            <h5 class="o_dropdown_title d-inline">Group By</h5>
                        </div>
                        <t t-set="currentGroup" t-value="null"/>
                        <t t-foreach="groupByItems" t-as="item" t-key="item.id">
                            <t t-if="currentGroup !== null and currentGroup !== item.groupNumber">
                                <div class="dropdown-divider" role="separator"/>
                            </t>
                            <t t-if="item.fieldType === 'properties'">
                                <PropertiesGroupByItem item="item" onGroup.bind="onGroupBySelected"/>
                            </t>
                            <t t-elif="item.options">
                                <AccordionItem class="'text-truncate'" description="item.description" selected="item.isActive">
                                    <t t-set="subGroup" t-value="null"/>
                                    <t t-foreach="item.options" t-as="option" t-key="option.id">
                                        <t t-if="subGroup !== null and subGroup !== option.groupNumber">
                                            <div class="dropdown-divider" role="separator"/>
                                        </t>
                                        <CheckboxItem class="{ o_item_option: true, selected: option.isActive }"
                                                            checked="option.isActive ? true : false"
                                                            closingMode="'none'"
                                                            t-esc="option.description"
                                                            attrs="{ title: option.description.length > 15 ? option.description : ''}"
                                                            onSelected="() => this.onGroupBySelected({ itemId: item.id, optionId: option.id})"
                                        />
                                        <t t-set="subGroup" t-value="option.groupNumber"/>
                                    </t>
                                </AccordionItem>
                            </t>
                            <t t-else="">
                                <CheckboxItem class="{ 'o_menu_item text-truncate': true, selected: item.isActive }"
                                                    checked="item.isActive"
                                                    closingMode="'none'"
                                                    t-esc="item.description"
                                                    attrs="{ title: item.description.length > 15 ? item.description : ''}"
                                                    onSelected="() => this.onGroupBySelected({ itemId: item.id })"
                                />
                            </t>
                            <t t-set="currentGroup" t-value="item.groupNumber"/>
                        </t>
                        <t t-if="!hideCustomGroupBy and fields.length">
                            <div t-if="groupByItems.length" role="separator" class="dropdown-divider"/>
                            <CustomGroupByItem fields="fields" onAddCustomGroup.bind="onAddCustomGroup"/>
                        </t>
                    </div>
                </t>
                <!-- Comparison -->
                <t t-if="showComparisonMenu">
                    <div class="o_dropdown_container o_comparison_menu w-100 w-lg-auto h-100 px-3 border-end">
                        <div class="px-3 fs-5 mb-2">
                            <i class="me-2 text-danger" t-att-class="facet_icons.comparison"/>
                            <h5 class="o_dropdown_title d-inline">Comparison</h5>
                        </div>
                        <t t-foreach="comparisonItems" t-as="item" t-key="item.id">
                            <CheckboxItem class="{ 'o_menu_item text-truncate': true, selected: item.isActive }"
                                                checked="item.isActive"
                                                closingMode="'none'"
                                                t-esc="item.description"
                                                attrs="{ title: item.description.length > 15 ? item.description : ''}"
                                                onSelected="() => this.onComparisonSelected(item.id)"
                            />
                        </t>
                    </div>
                </t>
                <!-- Favorite -->
                <t t-if="this.env.searchModel.searchMenuTypes.has('favorite')">
                    <div class="o_dropdown_container o_favorite_menu w-100 w-lg-auto h-100 px-3">
                        <t t-set="sharedFavoritesItems" t-value="sharedFavorites" />
                        <t t-set="favoriteItems" t-value="favorites" />
                        <div class="px-3 fs-5 mb-2">
                            <i class="me-2 text-favourite" t-att-class="facet_icons.favorite"/>
                            <h5 class="o_dropdown_title d-inline">Favorites</h5>
                        </div>
                        <t t-foreach="favoriteItems" t-as="item" t-key="item.id">
                            <t t-call="web.SearchBarMenu.FavoriteItem" />
                        </t>
                        <t t-if="favoriteItems.length">
                            <div role="separator" class="dropdown-divider"/>
                        </t>
                        <t t-if="sharedFavoritesItems.length > 10">
                            <AccordionItem class="'o_shared_favorites text-truncate'" description.translate="Shared filters">
                                <t t-foreach="sharedFavoritesItems" t-as="item" t-key="item.id">
                                    <t t-call="web.SearchBarMenu.FavoriteItem" />
                                </t>
                            </AccordionItem>
                        </t>
                        <t t-else="">
                            <t t-foreach="sharedFavoritesItems" t-as="item" t-key="item.id">
                                <t t-call="web.SearchBarMenu.FavoriteItem" />
                            </t>
                        </t>
                        <div t-if="sharedFavoritesItems.length" role="separator" class="dropdown-divider"/>
                        <t t-set="currentGroup" t-value="null"/>
                        <t t-foreach="otherItems" t-as="item" t-key="item.key">
                            <t t-if="currentGroup !== null and currentGroup !== item.groupNumber">
                                <div role="separator" class="dropdown-divider"/>
                            </t>
                            <t t-component="item.Component"/>
                            <t t-set="currentGroup" t-value="item.groupNumber"/>
                        </t>
                    </div>
                </t>
                <t t-slot="default"/>
            </t>
        </Dropdown>
    </t>

    <t t-name="web.SearchBarMenu.FavoriteItem">
        <t t-set="item" t-value="item" />
        <CheckboxItem class="{ 'o_menu_item text-truncate': true, selected: item.isActive }"
                            checked="item.isActive"
                            closingMode="'none'"
                            onSelected="() => this.onFavoriteSelected(item.id)"
        >
            <span class="d-flex p-0 align-items-center justify-content-between">
                <span t-esc="item.description" t-att-title="item.description.length > 15 ? item.description : ''" class="text-truncate flex-grow-1"/>
                <i class="ms-1 fa fa-trash-o"
                    title="Delete item"
                    t-on-click.stop="() => this.openConfirmationDialog(item.id, item.userId)"
                />
            </span>
        </CheckboxItem>
    </t>

</templates>
