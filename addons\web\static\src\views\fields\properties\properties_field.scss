.o_field_properties {
    width: 100%;
    // be sure that the property field can take zero height
    // in case no property exists
    display: block;
    margin-bottom: 0 !important;
}

// The web client will add a "o_field_invalid" class when the field is considered
// as not valid (at least one empty label). In that case we want to have in red
// only the empty label, and not all inputs / components.
.o_field_properties,
.o_field_properties.o_field_invalid,
.o_property_field_popover {
    .o_input,
    .o_dropdown_button,
    .o_datepicker_button {
        @include print-variable(o-input-border-color, $o-form-lightsecondary);
        color: $o-main-text-color !important;
    }
    .o_dropdown_button,
    .o_datepicker_button {
        color: initial !important;
    }
    .o_input_dropdown {
        color: $o-main-text-color !important;
    }

    & {
        .o_input:focus,
        .dropdown:focus ~ .o_dropdown_button,
        .dropdown:focus-within ~ .o_dropdown_button,
        .o_input:focus ~ .o_datepicker_button,
        .o_dropdown_button:focus {
            @include print-variable(o-input-border-color, $o-action);
            * {
                @include print-variable(o-input-border-color, $o-action);
            }
        }
    }
    // only the empty property label should be affected by `o_field_invalid`
    .o_field_property_empty_label {
        color: map-get($theme-colors, 'danger');
    }
}

.o_property_field_highlight .o_field_property_empty_label,
.o_property_field_highlight {
    border: 1px solid $primary;
    padding: 5px;
    margin: -5px;
    border-radius: 3px;
}

.o_field_property_label {
    width: 180px;

    &:hover .o_field_property_open_popover,
    .o_field_property_open_popover:focus,
    &:hover .oi-draggable,
    .oi-draggable:focus {
        opacity: 1;
    }
    .o_field_property_open_popover,
    .oi-draggable {
        opacity: 0;
        cursor: pointer;
        transition: 0.1s;
    }
    .o_field_property_open_popover:hover,
    .oi-draggable:hover {
        color: $primary;
    }
}

.o_field_property_add {
    // cancel button padding to align the
    // text button with properties labels
    margin-left: -1rem;
}

.o_property_folded {
    .o_field_property_label,
    .o_property_field_value {
        display: none !important;
    }
}

.o_property_field_popover {
    font-size: inherit;
    box-sizing: border-box;

    // fullscreen on small screen sizes
    @include media-breakpoint-down(md) {
        top: 0px !important;
        left: 0px !important;
        right: 0px;
        bottom: 0px;
        margin-top: 0px !important;
    }
}

.o_field_property_group_label {
    cursor: pointer;
    user-select: none;
    height: 20px;
    box-shadow: 0 $border-width 0 $o-form-separator-color;
}

.o_field_properties {
    .oi-draggable {
        cursor: url(/web/static/img/openhand.cur), grab;
    }
    @include media-breakpoint-down(sm) {
        .o_field_property_open_popover {
            opacity: 1;
            &.disabled {
                pointer-events: none;
            }
        }
    }
}

.o_property_dragging {
    // make the background transparent for "o_property_drag_group" effect
    input {
        background: transparent;
    }

    // disabled click interaction when dragging on those elements
    .o_field_property_open_popover,
    .o_field_property_label,
    input {
        pointer-events: none;
    }

    .o_property_drag_item {
        opacity: 0.7;
        transform: rotate(2deg);
    }

    .o_field_property_add {
        display: none;
    }
}

.o_field_properties div:not([columns="1"]) .o_property_drag_group {
    // highlight effect on the group is activated only in multi-columns
    box-sizing: border-box;
    background-color: #{tint-color($o-action, 90%)};
    border: 1px solid #{$o-action};
}
