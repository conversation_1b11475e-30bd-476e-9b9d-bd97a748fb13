///============================================================================
/// Odoo SCSS Functions
///
/// Regroup functions that can be used at any stage of the compilation process.
///============================================================================


/// Remove the unit of a length
/// ---------------------------------------------------------------------------
/// @param {Number} $-value
///     Number to remove unit from
/// @return {$-value}
///     Unitless number
@function o-strip-unit($-value) {
    @if type-of($-value) == 'number' and not unitless($-value) {
        @return $-value / ($-value * 0 + 1);
    }

    @return $-value;
}

/// Converts px to rem.
/// ---------------------------------------------------------------------------
/// @param {number or px value} $-px-value
///     The value to convert.
/// @param {number or px value } $-base
///     The base font-size
/// @return {number}
///    `$-px-value` converted in rem units
@function o-to-rem($-px-value, $-base: 16) {
    @return (o-strip-unit($-px-value) / o-strip-unit($-base)) * 1rem;
}


/// Mixins
/// ===========================================================================

/// Generate Bootstrap, BS-legacy and custom text/bg classes in a consistent
/// way. It applies the desired color to a specified rule using CSS variables.
/// ---------------------------------------------------------------------------
/// @param {color} $-color
///     Desidered color
/// @param {string} $-rule
///     CSS property to be affected
/// @param {string} $-css-opacity-var
///     CSS varible defining the opacity
/// ---------------------------------------------------------------------------
@mixin o-print-color($-color, $-rule, $-css-opacity-var) {
    --#{$-rule}: RGBA(#{to-rgb($-color)}, var(--#{$-css-opacity-var}, 1));
    #{$-rule}: var(--#{$-rule}) !important;
}
