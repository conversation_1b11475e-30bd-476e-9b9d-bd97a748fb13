<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.DynamicModelFieldSelectorChar" t-inherit="web.CharField" t-inherit-mode="primary">
        <xpath expr="//input" position="attributes">
            <attribute name="class" add="d-none" separator=" "/>
        </xpath>
        <xpath expr="//span" position="attributes">
            <attribute name="class" add="d-none" separator=" "/>
        </xpath>
        <xpath expr="//t[@t-if='props.readonly']" position="before">
            <DynamicModelFieldSelector t-props="getSelectorProps" />
        </xpath>
    </t>
</templates>
