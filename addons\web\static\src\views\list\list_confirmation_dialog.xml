<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.ListView.ConfirmationModal">
        <Dialog size="'md'" title="props.title">
            <main role="alert">
                <p>
                    <t t-if="props.isDomainSelected">This update will only consider the records of the current page.<br/><br/></t>
                    <t t-if="props.nbRecords != props.nbValidRecords">
                        Among the <t t-esc="props.nbRecords"/> selected records,
                        <t t-esc="props.nbValidRecords"/> are valid for this update.<br/>
                    </t>
                    Are you sure you want to perform the following update on those <t t-esc="props.nbValidRecords"/> records?
                </p>
                <div class="table-responsive">
                    <table class="o_modal_changes">
                        <tbody>
                            <t t-foreach="props.fields" t-as="field" t-key="field_index">
                                <tr>
                                    <td>Field:</td>
                                    <td><t t-esc="field.label"/></td>
                                </tr>
                                <tr>
                                    <td>Update to:</td>
                                    <td style="pointer-events: none;">
                                        <Field name="field.name" record="props.record" type="field.widget" readonly="true" fieldInfo="field.fieldNode"/>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
            </main>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="_confirm" t-ref="autofocus">
                Confirm
                </button>
                <button t-if="props.cancel" class="btn btn-secondary" t-on-click="_cancel">
                Cancel
                </button>
            </t>
        </Dialog>
    </t>

</templates>
