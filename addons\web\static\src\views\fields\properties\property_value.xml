<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.PropertyValue">
        <t t-if="props.readonly and !['boolean', 'many2one', 'many2many', 'tags'].includes(props.type)">
            <span t-if="displayValue" t-out="displayValue"/>
        </t>
        <t t-else="">
            <t t-if="props.type === 'boolean'">
                <CheckBox
                    id="props.id"
                    value="propertyValue"
                    disabled="props.readonly"
                    onChange.bind="onValueChange"
                />
            </t>
            <t t-elif="['date', 'datetime'].includes(props.type)">
                <DateTimeInput
                    id="props.id"
                    type="props.type"
                    value="propertyValue"
                    onChange.bind="onValueChange"
                />
            </t>
            <t t-elif="props.type === 'integer'">
                <input
                    t-att-id="props.id"
                    class="o_input o_field_property_input"
                    type="text"
                    autocomplete="off"
                    t-att-value="displayValue"
                    t-on-change="(event) => this.onValueChange(event.target.value)"
                />
            </t>
            <t t-elif="props.type === 'float'">
                <input
                    t-att-id="props.id"
                    class="o_input o_field_property_input"
                    type="text"
                    autocomplete="off"
                    t-att-value="displayValue"
                    t-on-change="(event) => this.onValueChange(event.target.value)"
                />
            </t>
            <t t-elif="props.type === 'selection'">
                <select class="o_input" t-on-change="event => this.onValueChange(event.target.value)"
                    t-att-id="props.id">
                    <option t-att-value="false" />
                    <t t-foreach="props.selection || []" t-as="option" t-key="option[0]">
                        <option
                            t-att-selected="option[0] === propertyValue"
                            t-att-value="option[0]"
                            t-out="option[1]"
                        />
                    </t>
                </select>
            </t>
            <t t-elif="props.type === 'tags'">
                <PropertyTags
                    id="props.id"
                    selectedTags="propertyValue"
                    tags="props.tags || []"
                    deleteAction="'value'"
                    readonly="props.readonly"
                    canChangeTags="props.canChangeDefinition"
                    checkDefinitionWriteAccess.bind="props.checkDefinitionWriteAccess ? props.checkDefinitionWriteAccess : () => {}"
                    onValueChange.bind="onValueChange"
                    onTagsChange.bind="props.onTagsChange"/>
            </t>
            <div
                t-elif="props.type === 'many2one'"
                class="o_field_property_many2one_value d-flex flex-row align-items-center"
                t-att-class="{'avatar': showAvatar}">
                <t t-if="props.comodel">
                    <t t-set="hasAccess" t-value="!propertyValue || propertyValue[1]"/>
                    <img t-if="showAvatar and propertyValue[0] and hasAccess"
                        t-attf-src="/web/image/{{props.comodel}}/{{propertyValue[0]}}/avatar_128"
                        class="o_avatar rounded me-1"/>
                    <t t-if="props.readonly || !hasAccess">
                        <a
                            t-if="clickableRelational || !hasAccess"
                            t-attf-class="o_form_uri {{propertyValue and propertyValue[1] ? '' : 'disabled'}}"
                            t-att-href="propertyValue[0] ? `#id=${propertyValue[0]}&amp;model=${props.comodel}` : '#'"
                            t-on-click.prevent="onMany2oneClick">
                            <t t-if="hasAccess" t-out="propertyValue[1]"/>
                            <t t-else="">No Access</t>
                        </a>
                        <span
                            t-else=""
                            t-out="propertyValue and propertyValue[1] || ''" />
                    </t>
                    <Many2XAutocomplete
                        t-else=""
                        t-key="props.id + '_' + props.comodel + '_' + props.string"
                        value="propertyValue[1] || ''"
                        id="props.id"
                        resModel="props.comodel"
                        autoSelect="true"
                        fieldString="props.string"
                        update.bind="onValueChange"
                        context="props.context"
                        getDomain.bind="() => propertyDomain"
                        quickCreate.bind="onQuickCreate"
                        activeActions="{ create: true, createEdit: true, write: true }"
                    />
                    <button
                        t-if="!props.readonly and propertyValue and propertyValue[0] and hasAccess"
                        type="button"
                        class="btn btn-secondary oi oi-arrow-right o_external_button o_properties_external_button py-0"
                        tabindex="-1"
                        draggable="false"
                        aria-label="Internal link"
                        data-tooltip="Internal link"
                        t-on-click="onExternalLinkClick"
                    />
                </t>
            </div>
            <t t-elif="props.type === 'many2many'">
                <div t-if="props.comodel" class="o_field_property_many2many_value d-flex flex-wrap flex-row gap-1 ms-0 pe-1 w-100 "
                    t-attf-class="o_field_widget o_field_many2many_tags {{showAvatar ? 'avatar' : ''}} {{props.readonly ? 'readonly' : ''}}">
                    <!-- Copy many2many tags style without duplicating all CSS -->
                    <TagsList tags="propertyValue"/>
                    <Many2XAutocomplete
                        t-if="!props.readonly"
                        t-key="props.id + '_' + props.comodel + '_' + props.string"
                        id="props.id"
                        resModel="props.comodel"
                        autoSelect="true"
                        fieldString="props.string"
                        update.bind="onValueChange"
                        context="props.context"
                        getDomain.bind="() => propertyDomain"
                        quickCreate.bind="onQuickCreate"
                        activeActions="{ create: true, createEdit: true, write: true }"
                        isToMany="true"
                    />
                </div>
            </t>
            <t t-else="">
                <input
                    class="o_input"
                    t-att-id="props.id"
                    type="text"
                    autocomplete="off"
                    t-att-value="propertyValue || ''"
                    t-on-change="(event) => this.onValueChange(event.target.value)"
                />
            </t>
        </t>
    </t>
</templates>
