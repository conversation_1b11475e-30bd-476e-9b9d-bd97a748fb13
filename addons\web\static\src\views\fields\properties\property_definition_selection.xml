<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.PropertyDefinitionSelection">
        <div class="o_field_property_selection d-flex"
            t-ref="propertyDefinitionSelection">
            <div class="w-100">
                <div
                    t-foreach="optionsVisible" t-as="option" t-key="option[0]"
                    class="o_field_property_selection_option d-flex flex-row align-items-center justify-content-between mb-2 rounded"
                    t-att-option-name="option[0]">
                    <i t-if="!props.readonly and props.canChangeDefinition"
                        class="o_field_property_selection_drag oi oi-draggable ui-sortable-handle me-2 text-center"/>
                    <span t-if="props.readonly" t-out="option[1]"/>
                    <input
                        t-else=""
                        class="o_input me-3"
                        type="text"
                        placeholder="Option Name"
                        t-att-value="option[1]"
                        t-on-change="(event) => this.onOptionChange(event, option_index)"
                        t-on-blur="(event) => this.onOptionBlur(event, option_index)"
                        t-on-keydown="(event) => this.onOptionKeyDown(event, option_index)"
                    />
                    <button
                        class="btn btn-link m-0 p-0 text-muted fs-5"
                        title="Select Default"
                        t-on-click="() => this.onOptionSetDefault(option_index)">
                        <i t-att-class="option[0] === props.default ? 'fa fa-star align-bottom' : 'fa fa-star-o align-bottom'"/>
                    </button>
                    <button
                        t-if="!props.readonly"
                        class="btn btn-link link-danger ms-2 p-0 fs-5"
                        title="Remove Property"
                        t-on-click="() => this.onOptionDelete(option_index)">
                        <i class="fa fa-trash-o align-bottom"/>
                    </button>
                </div>
                <button
                    t-if="!props.readonly"
                    class="btn btn-link btn-sm"
                    t-on-click="() => this.onOptionCreate(this.options.length)"
                    t-ref="addButton">
                    <i class="fa fa-plus"/>
                    Add a Value
                </button>
            </div>
        </div>
    </t>
</templates>
