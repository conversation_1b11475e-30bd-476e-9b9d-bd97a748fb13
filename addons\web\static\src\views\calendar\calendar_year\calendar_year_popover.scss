.o_cw_popover_link {
    margin-top: 2px;

    &[class*="o_calendar_color_"] {
        --o-bg-opacity: 1;

        border-radius: $border-radius;
        border-color: var(--o-cw-popover-color, #{$info});
        padding: 0 map-get($spacers, 1);
        background: RGBA(var(--o-cw-popover-color--subtle, #{to-rgb($info)}), var(--o-bg-opacity));
        font-weight: $font-weight-normal;

        &:hover {
            --o-bg-opacity: .5 !important;
        }

        &.o_event_dot {
            --o-bg-opacity: 0;

            &:before {
                margin-right: map-get($spacers, 1);
                font-family: FontAwesome;
                font-size: $o-font-size-base-smaller;
                color: var(--o-cw-popover-color, #{$info});
                content: '\f111';
            }

        }
    }

    @for $i from 1 through length($o-colors-complete) {
        $color: nth($o-colors-complete, $i);
        $color-subtle: mix($o-white, $color, 55%);

        &.o_calendar_color_#{$i - 1} {
            --o-cw-popover-color: #{$color};
            --o-cw-popover-color--subtle: #{$color-subtle};

            color: color-contrast($color-subtle);
        }
    }
}
