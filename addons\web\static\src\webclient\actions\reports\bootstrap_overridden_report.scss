// Grid columns
$container-padding-x: 16px !default;

// Options
$enable-rfs: false !default;

// Colors
$body-bg: $o-white !default;
$body-color: $o-gray-900 !default;

// Fonts
$font-size-base: 1rem !default;
$text-muted: $o-gray-600 !default;
$font-weight-bold: $o-font-weight-bold !default;

//-- remove Emoji fonts
$font-family-sans-serif: o-add-unicode-support-font(("Lucida Grande", Helvetica, Verdana, Arial, sans-serif), 1) !default;

// Set hx font-sizes
$headings-font-family: inherit !default;
$headings-font-weight: $o-font-weight-medium !default;

$h1-font-size: $font-size-base * 2.5 !default;
$h2-font-size: $font-size-base * 2 !default;
$h3-font-size: $font-size-base * 1.75 !default;
$h4-font-size: $font-size-base * 1.5 !default;
$h5-font-size: $font-size-base * 1.25 !default;

$headings-margin-bottom: .75rem !default;

// Borders
$border-color: $o-gray-900 !default;
$border-radius-lg: .75rem !default;

// Tables
$table-cell-padding-y: .5rem !default;
$table-cell-padding-x: .5rem !default;
$table-cell-padding-y-sm: .25rem !default;
$table-cell-padding-x-sm: .25rem !default;

$table-th-font-weight: null !default;

$table-color: $body-color !default;
$table-bg: transparent !default;
