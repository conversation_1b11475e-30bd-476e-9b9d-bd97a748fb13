<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.EmailField">
        <t t-if="props.readonly">
            <div class="d-grid">
                <a class="o_form_uri o_text_overflow" t-on-click.stop="" t-att-href="props.record.data[props.name] ? 'mailto:'+props.record.data[props.name] : undefined" t-esc="props.record.data[props.name] || ''"/>
            </div>
        </t>
        <t t-else="">
            <div class="d-inline-flex w-100">
                <input
                    class="o_input"
                    t-att-id="props.id"
                    type="email"
                    autocomplete="off"
                    t-att-placeholder="props.placeholder"
                    t-att-required="props.required"
                    t-ref="input"
                />
            </div>
        </t>
    </t>

    <t t-name="web.FormEmailField" t-inherit="web.EmailField" t-inherit-mode="primary">
        <xpath expr="//input" position="after">
            <a
                t-if="props.record.data[props.name]"
                t-att-href="'mailto:'+props.record.data[props.name]"
                class="ms-3 d-inline-flex align-items-center"
                target="_blank"
            >
                <i class="fa fa-envelope" data-tooltip="Send Email" aria-label="Send Email"></i>
            </a>
        </xpath>
    </t>

</templates>
